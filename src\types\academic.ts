// ----------------------------------------------------------------------
// Academic Management Types
// ----------------------------------------------------------------------

export type Class = {
  id: string;
  code: string; // Mã lớp
  name: string; // Tên học phần (VN)
  nameEn?: string; // Tên học phần (EN)
  term: string; // Học kỳ: 2025-2026-1
  size: number; // S<PERSON> số
  nextSessionAt?: string; // ISO date string
  teacherId?: string;
  room?: string;
  schedule?: string; // Lịch học
  status?: 'active' | 'inactive' | 'completed';
  createdAt?: string;
  updatedAt?: string;
};

export type Student = {
  _id: string;
  studentId: string; // MSSV
  fullName: string;
  email?: string;
  phone?: string;
  status?: 'active' | 'inactive' | 'graduated' | 'dropped';
  enrolledAt?: string;
  avatar?: string;
};

export type AttendanceSession = {
  id: string;
  classId: string;
  date: string; // ISO date string
  slot?: string; // Ca học (1-2, 3-4, 5-6, 7-8, 9-10)
  note?: string;
  locked?: boolean; // Đã chốt phiên hay chưa
  totalStudents?: number;
  presentCount?: number;
  absentCount?: number;
  lateCount?: number;
  excusedCount?: number;
  createdAt?: string;
  updatedAt?: string;
  // Additional display fields
  title?: string;
  createdBy?: string;
  className?: string;
  classCode?: string;
};

export type AttendanceRecord = {
  sessionId: string;
  studentId: string;
  status: 'present' | 'absent' | 'late' | 'excused';
  note?: string;
  checkedAt?: string; // Thời gian check-in
  updatedAt?: string;
};

export type GradeItem = {
  id: string;
  classId: string;
  name: string; // Giữa kỳ / Cuối kỳ / Bài tập / Chuyên cần...
  weight: number; // Trọng số 0..1 (ví dụ: 0.3 = 30%)
  maxScore: number; // Điểm tối đa (10, 100...)
  type?: 'midterm' | 'final' | 'assignment' | 'attendance' | 'quiz' | 'project';
  description?: string;
  dueDate?: string; // Hạn nộp (nếu có)
  isPublished?: boolean; // Đã công bố điểm hay chưa
  createdAt?: string;
  updatedAt?: string;
};

export type GradeRecord = {
  itemId: string;
  studentId: string;
  score: number | null; // null = chưa có điểm
  note?: string;
  gradedAt?: string;
  updatedAt?: string;
};

// Utility types for UI
export type ClassWithStats = Class & {
  totalStudents: number;
  attendanceRate?: number;
  lastAttendanceDate?: string;
  upcomingSession?: AttendanceSession;
};

export type StudentWithGrades = Student & {
  _id: string;
  grades?: GradeRecord[];
  totalScore?: number;
  attendanceRate?: number;
  lastAttendance?: AttendanceRecord;
};

export type AttendanceSessionWithRecords = AttendanceSession & {
  records?: AttendanceRecord[];
  students?: Student[];
};

export type GradeItemWithRecords = GradeItem & {
  records?: GradeRecord[];
  averageScore?: number;
  submissionRate?: number;
};

// Filter and search types
export type ClassFilter = {
  term?: string; // legacy
  year?: string | number;
  semester?: string; // '1' | '2' | '3'
  status?: Class['status'];
  search?: string;
};

export type StudentFilter = {
  status?: Student['status'];
  search?: string;
};

export type AttendanceFilter = {
  dateFrom?: string;
  dateTo?: string;
  status?: AttendanceRecord['status'];
};

export type GradeFilter = {
  type?: GradeItem['type'];
  isPublished?: boolean;
};

// Form schemas
export type CreateClassForm = Omit<Class, 'id' | 'createdAt' | 'updatedAt'>;
export type UpdateClassForm = Partial<CreateClassForm>;

export type CreateStudentForm = Omit<Student, 'id' | 'enrolledAt'>;
export type UpdateStudentForm = Partial<CreateStudentForm>;

export type CreateAttendanceSessionForm = Omit<
  AttendanceSession,
  | 'id'
  | 'totalStudents'
  | 'presentCount'
  | 'absentCount'
  | 'lateCount'
  | 'excusedCount'
  | 'createdAt'
  | 'updatedAt'
>;

export type CreateGradeItemForm = Omit<GradeItem, 'id' | 'createdAt' | 'updatedAt'>;
export type UpdateGradeItemForm = Partial<CreateGradeItemForm>;

export type BulkGradeUpdateForm = {
  itemId: string;
  grades: Array<{
    studentId: string;
    score: number | null;
    note?: string;
  }>;
};

// API Response types
export type ApiResponse<T> = {
  data: T;
  message?: string;
  success: boolean;
};

export type PaginatedResponse<T> = {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
};

// Dashboard stats
export type DashboardStats = {
  totalClasses: number;
  totalStudents: number;
  upcomingSessions: number;
  pendingGrades: number;
  averageAttendanceRate: number;
  recentActivities: Array<{
    id: string;
    type: 'attendance' | 'grade' | 'class';
    message: string;
    timestamp: string;
    classId?: string;
    className?: string;
  }>;
};

// Report types
export type AttendanceReport = {
  classId: string;
  className: string;
  term: string;
  totalSessions: number;
  averageAttendanceRate: number;
  studentStats: Array<{
    studentId: string;
    studentName: string;
    presentCount: number;
    absentCount: number;
    lateCount: number;
    excusedCount: number;
    attendanceRate: number;
  }>;
};

export type GradeReport = {
  classId: string;
  className: string;
  term: string;
  gradeItems: Array<{
    itemId: string;
    itemName: string;
    weight: number;
    averageScore: number;
    maxScore: number;
    submissionRate: number;
  }>;
  studentGrades: Array<{
    studentId: string;
    studentName: string;
    grades: Record<string, number | null>; // itemId -> score
    totalScore: number;
    rank: number;
  }>;
};

// Grade Component types for TTU API
export type GradeComponent = {
  _id: number;
  year: number;
  semester: number;
  class: number;
  course: number;
  name_eng: string;
  name_vie: string;
  weight: number;
  description?: string | null;
  note?: string | null;
  created_by: string;
};

export type CreateGradeComponentForm = {
  year: number;
  semester: number;
  class: number;
  course: number;
  name_eng: string;
  name_vie: string;
  weight: number;
  description?: string;
  note?: string;
};

export type GradeComponentFilter = {
  page?: number;
  perPage?: number;
  _class?: number;
};

export type ComponentScore = {
  _id: string;
  year: number;
  semester: number;
  grade_component: number;
  class: number;
  course: number;
  school: number;
  cohort: number;
  student: number;
  ten_point_scale: number;
  description?: string;
  note?: string;
  created_at?: string;
  updated_at?: string;
  // Populated fields
  student_info?: Student;
  grade_component_info?: GradeComponent;
};

export type CreateComponentScoreForm = {
  year: number;
  semester: number;
  grade_component: number;
  class: number;
  course: number;
  school: number;
  cohort: number;
  student: number;
  ten_point_scale: number;
  description?: string;
  note?: string;
};
