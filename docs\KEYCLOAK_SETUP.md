# Hướng dẫn cấu hình Keycloak với oidc-client-ts

## Tổng quan

Dự án này đã được tích hợp với Keycloak sử dụng thư viện `oidc-client-ts` để xác thực người dùng. Keycloak là một giải pháp Identity and Access Management (IAM) mã nguồn mở mạnh mẽ.

## Cấu hình Keycloak Server

### 1. Tạo Realm

1. Đăng nhập vào Keycloak Admin Console
2. Tạo một realm mới (ví dụ: `ttu-class`)
3. Ghi nhớ tên realm để sử dụng trong cấu hình

### 2. Tạo Client

1. Trong realm vừa tạo, vào **Clients** > **Create client**
2. Cấu hình client:
   - **Client type**: OpenID Connect
   - **Client ID**: `ttu-class-frontend` (hoặc tên bạn muốn)
   - **Name**: TTU Class Management Frontend
   - **Description**: Frontend application for TTU Class Management

3. Trong tab **Settings**:
   - **Client authentication**: OFF (public client)
   - **Authorization**: OFF
   - **Standard flow**: ON
   - **Direct access grants**: OFF (khuyến nghị)
   - **Implicit flow**: OFF
   - **Service accounts roles**: OFF

4. Trong tab **Access settings**:
   - **Valid redirect URIs**: 
     - `http://localhost:8082/auth/keycloak/callback`
     - `https://your-domain.com/auth/keycloak/callback`
   - **Valid post logout redirect URIs**:
     - `http://localhost:8082/auth/keycloak/logout-callback`
     - `https://your-domain.com/auth/keycloak/logout-callback`
   - **Web origins**: 
     - `http://localhost:8082`
     - `https://your-domain.com`

### 3. Cấu hình User

1. Tạo user trong **Users** section
2. Đặt password trong tab **Credentials**
3. Cấu hình roles và groups nếu cần

## Cấu hình Ứng dụng

### 1. Biến môi trường

Tạo file `.env.local` hoặc cập nhật file `.env` với các biến sau:

```env
# Keycloak Configuration
NEXT_PUBLIC_KEYCLOAK_AUTHORITY=https://your-keycloak-domain/realms/ttu-class
NEXT_PUBLIC_KEYCLOAK_CLIENT_ID=ttu-class-frontend
NEXT_PUBLIC_KEYCLOAK_REDIRECT_URI=http://localhost:8082/auth/keycloak/callback
NEXT_PUBLIC_KEYCLOAK_POST_LOGOUT_REDIRECT_URI=http://localhost:8082/auth/keycloak/logout-callback
NEXT_PUBLIC_KEYCLOAK_SCOPE=openid profile email
```

### 2. Cấu hình Auth Method

Trong file `src/global-config.ts`, đặt auth method thành `keycloak`:

```typescript
auth: {
  method: 'keycloak',
  skip: false,
  redirectPath: paths.dashboard.root,
},
```

## Cách sử dụng

### 1. Đăng nhập

- Người dùng truy cập trang đăng nhập
- Click vào nút "Đăng nhập với Keycloak"
- Được chuyển hướng đến Keycloak login page
- Sau khi đăng nhập thành công, được chuyển về ứng dụng

### 2. Đăng xuất

- Click vào nút đăng xuất trong ứng dụng
- Được chuyển hướng đến Keycloak logout page
- Sau khi đăng xuất, được chuyển về trang đăng nhập

### 3. Token Management

- Access token được tự động lưu và sử dụng cho API calls
- Token được tự động refresh khi hết hạn
- Token được xóa khi đăng xuất

## Tính năng

### ✅ Đã triển khai

- [x] Đăng nhập với Keycloak
- [x] Đăng xuất với Keycloak
- [x] Tự động refresh token
- [x] Callback handling
- [x] Token storage và management
- [x] Integration với axios interceptors

### ❌ Chưa triển khai (do Keycloak quản lý)

- [ ] Đăng ký tài khoản (quản lý qua Keycloak Admin)
- [ ] Quên mật khẩu (sử dụng Keycloak forgot password flow)
- [ ] Đổi mật khẩu (sử dụng Keycloak account management)

## Troubleshooting

### 1. CORS Issues

Đảm bảo Keycloak được cấu hình đúng Web Origins trong client settings.

### 2. Redirect URI Mismatch

Kiểm tra lại Valid redirect URIs trong Keycloak client settings phải khớp với URL trong biến môi trường.

### 3. Token Issues

- Kiểm tra scope configuration
- Đảm bảo client có quyền truy cập các scope cần thiết
- Kiểm tra token expiration settings

### 4. Silent Renew Issues

Đảm bảo file `public/silent-renew.html` tồn tại và có thể truy cập được.

## Bảo mật

### Khuyến nghị

1. Sử dụng HTTPS trong production
2. Cấu hình đúng CORS policies
3. Sử dụng secure cookies
4. Cấu hình token expiration phù hợp
5. Thường xuyên cập nhật Keycloak và dependencies

### Client Configuration

- Sử dụng public client (không có client secret)
- Tắt implicit flow
- Chỉ bật standard flow (authorization code)
- Cấu hình chính xác redirect URIs

## Tài liệu tham khảo

- [Keycloak Documentation](https://www.keycloak.org/documentation)
- [oidc-client-ts Documentation](https://github.com/authts/oidc-client-ts)
- [OpenID Connect Specification](https://openid.net/connect/)
