'use client';

import type { ClassWithStats } from 'src/types/academic';

import Stack from '@mui/material/Stack';
import Checkbox from '@mui/material/Checkbox';
import TableRow from '@mui/material/TableRow';
import TableCell from '@mui/material/TableCell';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { Iconify } from 'src/components/iconify';

type Props = {
  row: ClassWithStats;
  selected: boolean;
  onSelectRow: () => void;
};

export default function ClassTableRow({ row, selected, onSelectRow }: Props) {
  return (
    <TableRow hover selected={selected} role="checkbox" aria-checked={selected} tabIndex={-1}>
      <TableCell padding="checkbox">
        <Checkbox checked={selected} onClick={onSelectRow} />
      </TableCell>

      <TableCell sx={{ whiteSpace: 'nowrap', fontWeight: 600 }}>{row.code}</TableCell>

      <TableCell>
        <Typography
          variant="body2"
          sx={{ fontWeight: 600, textDecoration: 'none', color: '#000' }}
          component={RouterLink}
          href={paths.dashboard.classes.details(row.id)}
        >
          {row.name}
        </Typography>
        {!!row.nameEn && (
          <Typography variant="caption" sx={{ color: 'text.secondary', display: 'block' }}>
            {row.nameEn}
          </Typography>
        )}
      </TableCell>

      <TableCell sx={{ whiteSpace: 'nowrap' }}>{row.term}</TableCell>

      <TableCell align="right" sx={{ whiteSpace: 'nowrap' }}>
        <Stack direction="row" spacing={0.5} justifyContent="flex-end">
          <IconButton
            color="primary"
            component={RouterLink}
            href={paths.dashboard.classes.details(row.id)}
            aria-label="Xem chi tiết"
          >
            <Iconify icon="solar:eye-bold" />
          </IconButton>
        </Stack>
      </TableCell>
    </TableRow>
  );
}
