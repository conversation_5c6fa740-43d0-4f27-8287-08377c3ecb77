'use client';

import { useState, useEffect } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Chip from '@mui/material/Chip';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import CardHeader from '@mui/material/CardHeader';

import { DashboardContent } from 'src/layouts/dashboard';
import { updateAttendance, getAttendanceSession } from 'src/actions/ttu-api';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';

import AttendanceTaking from './attendance-taking';

type SessionInfo = {
  id: string;
  content: string;
  date: string;
  className?: string;
  classCode?: string;
  lecturerName?: string;
  createdBy?: string;
};

export default function AttendanceSessionDetailsView({
  classId,
  sessionId,
}: {
  classId: string;
  sessionId: string;
}) {
  const [sessionInfo, setSessionInfo] = useState<SessionInfo | null>(null);
  const [editableContent, setEditableContent] = useState('');
  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [tempContent, setTempContent] = useState('');

  // Load session info
  useEffect(() => {
    const loadSessionInfo = async () => {
      try {
        setLoading(true);
        const response = await getAttendanceSession(sessionId);

        const data = response?.data?.data; // API trả về object trực tiếp

        if (data) {
          const info: SessionInfo = {
            id: String(data._id) || sessionId,
            content: data.content || '',
            date: data.date || '',
            className: data.class?.name_vn || data.class?.name || '',
            classCode: data.code || '',
            lecturerName: data.lecturer
              ? `${data.lecturer.last_name} ${data.lecturer.first_name}`.trim()
              : '',
            createdBy: data.created_by || '',
          };

          setSessionInfo(info);
          setEditableContent(info.content);
          setTempContent(info.content);
        } else {
          // Fallback: create basic session info
          const fallbackInfo: SessionInfo = {
            id: sessionId,
            content: '',
            date: '',
            className: '',
            classCode: '',
            lecturerName: '',
            createdBy: '',
          };
          setSessionInfo(fallbackInfo);
          setEditableContent('');
          setTempContent('');
        }
      } catch (error) {
        console.error('Failed to load session info:', error);
        toast.error('Không thể tải thông tin phiên điểm danh');

        // Fallback: create basic session info even on error
        const fallbackInfo: SessionInfo = {
          id: sessionId,
          content: '',
          date: '',
          className: '',
          classCode: '',
          lecturerName: '',
          createdBy: '',
        };
        setSessionInfo(fallbackInfo);
        setEditableContent('');
        setTempContent('');
      } finally {
        setLoading(false);
      }
    };

    if (sessionId) {
      loadSessionInfo();
    }
  }, [sessionId]);

  // Handle edit mode
  const handleStartEdit = () => {
    setIsEditing(true);
    setTempContent(editableContent);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setTempContent(editableContent);
  };

  const handleSaveEdit = async () => {
    if (tempContent.trim() === '') {
      toast.warning('Tên bài học không được để trống');
      return;
    }

    if (tempContent.trim() === editableContent) {
      setIsEditing(false);
      return;
    }

    try {
      await updateAttendance(sessionId, {
        content: tempContent.trim(),
      });

      // Update local state
      setEditableContent(tempContent.trim());
      setSessionInfo((prev) => (prev ? { ...prev, content: tempContent.trim() } : null));
      setIsEditing(false);
      toast.success('Đã cập nhật tên bài học thành công');
    } catch (error) {
      console.error('Failed to update session content:', error);
      toast.error('Có lỗi xảy ra khi cập nhật tên bài học');
    }
  };

  const formatDate = (dateStr: string) => {
    if (!dateStr) return '';
    try {
      return new Date(dateStr).toLocaleDateString('vi-VN');
    } catch {
      return dateStr;
    }
  };

  return (
    <DashboardContent>
      <Card>
        <CardHeader
          sx={{ mb: 2 }}
          title={
            <Stack spacing={2}>
              <Typography variant="h5" component="h1">
                Phiên điểm danh
              </Typography>

              {!loading && (
                <Box>
                  {sessionInfo && (
                    <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 2 }}>
                      <Typography variant="h6" color="primary">
                        {sessionInfo.className || 'Phiên điểm danh'}
                      </Typography>
                      {sessionInfo.classCode && (
                        <Chip label={sessionInfo.classCode} size="small" variant="outlined" />
                      )}
                      {sessionInfo.date && (
                        <Chip
                          label={formatDate(sessionInfo.date)}
                          size="small"
                          color="info"
                          variant="outlined"
                        />
                      )}
                      {sessionInfo.lecturerName && (
                        <Chip
                          label={`GV: ${sessionInfo.lecturerName}`}
                          size="small"
                          color="secondary"
                          variant="outlined"
                        />
                      )}
                    </Stack>
                  )}

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      Tên bài học:
                    </Typography>

                    {isEditing ? (
                      <Stack direction="row" spacing={1} alignItems="center">
                        <TextField
                          fullWidth
                          size="small"
                          value={tempContent}
                          onChange={(e) => setTempContent(e.target.value)}
                          placeholder="Nhập tên bài học..."
                          autoFocus
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              handleSaveEdit();
                            } else if (e.key === 'Escape') {
                              handleCancelEdit();
                            }
                          }}
                        />
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={handleSaveEdit}
                          title="Lưu (Enter)"
                        >
                          <Iconify icon="eva:checkmark-fill" />
                        </IconButton>
                        <IconButton
                          size="small"
                          color="error"
                          onClick={handleCancelEdit}
                          title="Hủy (Esc)"
                        >
                          <Iconify icon="eva:close-fill" />
                        </IconButton>
                      </Stack>
                    ) : (
                      <Stack direction="row" spacing={1} alignItems="center">
                        <Typography
                          variant="h6"
                          sx={{
                            fontStyle: 'italic',
                            color: editableContent ? 'text.primary' : 'text.secondary',
                            flex: 1,
                          }}
                        >
                          {editableContent || 'Chưa có tên bài học'}
                        </Typography>
                        <Button
                          size="small"
                          variant="outlined"
                          startIcon={<Iconify icon="eva:edit-2-fill" />}
                          onClick={handleStartEdit}
                        >
                          Chỉnh sửa
                        </Button>
                      </Stack>
                    )}
                  </Box>
                </Box>
              )}

              {loading && (
                <Typography variant="body2" color="text.secondary">
                  Đang tải thông tin phiên điểm danh...
                </Typography>
              )}
            </Stack>
          }
        />

        <AttendanceTaking classId={classId} sessionId={sessionId} />
      </Card>
    </DashboardContent>
  );
}
