'use client';

import dayjs from 'dayjs';
import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

import Card from '@mui/material/Card';
import Grid from '@mui/material/Grid2';
import Alert from '@mui/material/Alert';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import InputLabel from '@mui/material/InputLabel';
import LoadingButton from '@mui/lab/LoadingButton';
import FormControl from '@mui/material/FormControl';
import OutlinedInput from '@mui/material/OutlinedInput';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';

import { paths } from 'src/routes/paths';

import { DashboardContent } from 'src/layouts/dashboard';
import { createAttendanceSessionApi } from 'src/actions/ttu-attendance-adapter';
import {
  fetchClassOptions,
  getCurrentSemester,
  getCurrentAcademicYear,
} from 'src/actions/ttu-report-adapter';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';

type AttendanceState = {
  year: string;
  semester: string;
  classId: string;
  date: string;
  content: string;
};

export default function AttendanceView() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const preSelectedClassId = searchParams.get('classId') || '';

  const [state, setState] = useState<AttendanceState>({
    year: getCurrentAcademicYear(),
    semester: getCurrentSemester(),
    classId: preSelectedClassId,
    date: dayjs().format('YYYY-MM-DD'),
    content: '',
  });

  const [classOptions, setClassOptions] = useState<Array<{ value: string; label: string }>>([]);
  const [creating, setCreating] = useState(false);

  // Load classes when year/semester changes
  useEffect(() => {
    const loadClasses = async () => {
      if (state.year) {
        try {
          const opts = await fetchClassOptions(state.year, state.semester);
          setClassOptions(opts);

          // If we have a pre-selected class but it's not in current options,
          // we might need to load different year/semester
          if (
            preSelectedClassId &&
            !opts.find((opt: { value: string; label: string }) => opt.value === preSelectedClassId)
          ) {
            // For now, just keep the classId - user can adjust year/semester if needed
            console.log('Pre-selected class not found in current year/semester options');
          }
        } catch (error) {
          console.error('Failed to load classes:', error);
        }
      } else {
        setClassOptions([]);
      }
    };
    loadClasses();
  }, [state.year, state.semester, preSelectedClassId]);

  const handleFieldChange = (field: keyof AttendanceState, value: any) => {
    setState((prev) => ({ ...prev, [field]: value }));
  };

  const handleYearChange = (val: dayjs.Dayjs | null) => {
    const year = val ? String(val.year()) : '';
    setState((prev) => ({
      ...prev,
      year,
      classId: '', // Reset class when year changes
    }));
  };

  const handleSemesterChange = (value: string) => {
    setState((prev) => ({
      ...prev,
      semester: value,
      classId: '', // Reset class when semester changes
    }));
  };

  const handleClassChange = (value: string) => {
    setState((prev) => ({
      ...prev,
      classId: value,
    }));
  };

  const handleDateChange = (val: dayjs.Dayjs | null) => {
    const date = val ? val.format('YYYY-MM-DD') : '';
    setState((prev) => ({ ...prev, date }));
  };

  const handleCreateAttendance = async () => {
    // Validation
    if (!state.classId) {
      toast.warning('Vui lòng chọn lớp học');
      return;
    }
    if (!state.date) {
      toast.warning('Vui lòng chọn ngày điểm danh');
      return;
    }
    if (!state.content.trim()) {
      toast.warning('Vui lòng nhập tiêu đề bài học');
      return;
    }

    try {
      setCreating(true);
      const result = await createAttendanceSessionApi({
        class: state.classId,
        date: state.date,
        content: state.content.trim(),
      });

      console.log('Full API response:', result);
      console.log('Result data:', result?.data);
      console.log('Result data.data:', result?.data?.data);
      console.log('pb320 value:', result?.data?.data?.pb320);
      console.log('pb320 type:', typeof result?.data?.data?.pb320);

      const attendanceId = result?.data?.data?.pb320;

      if (attendanceId) {
        toast.success('Tạo phiên điểm danh thành công! Đang chuyển đến trang điểm danh...');

        // Redirect to attendance session details page
        router.push(paths.dashboard.classes.attendanceSession(state.classId, String(attendanceId)));
      } else {
        console.error('AttendanceId is falsy:', attendanceId);
        throw new Error('Không nhận được ID phiên điểm danh hợp lệ');
      }
    } catch (error) {
      console.error('Failed to create attendance:', error);
      toast.error('Có lỗi xảy ra khi tạo phiên điểm danh');
    } finally {
      setCreating(false);
    }
  };

  const canCreateAttendance = state.classId && state.date && state.content.trim();

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="Điểm danh"
        links={[{ name: 'Trang chủ', href: paths.dashboard.root }, { name: 'Điểm danh' }]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Grid container spacing={3}>
        <Grid size={{ xs: 12 }}>
          <Card sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 3 }}>
              Tạo phiên điểm danh
            </Typography>

            {preSelectedClassId && (
              <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  Lớp học đã được chọn sẵn. Bạn có thể thay đổi năm học/học kỳ nếu cần.
                </Typography>
              </Alert>
            )}

            <Grid container spacing={3}>
              <Grid size={{ xs: 12, md: 6 }}>
                <FormControl fullWidth>
                  <DatePicker
                    views={['year']}
                    label="Năm học"
                    value={state.year ? dayjs(`${state.year}-01-01`) : null}
                    onChange={handleYearChange}
                  />
                </FormControl>
              </Grid>

              <Grid size={{ xs: 12, md: 6 }}>
                <FormControl fullWidth>
                  <InputLabel>Học kỳ</InputLabel>
                  <Select
                    value={state.semester}
                    onChange={(e) => handleSemesterChange(e.target.value)}
                    input={<OutlinedInput label="Học kỳ" />}
                    displayEmpty
                  >
                    <MenuItem value="1">Học kỳ 1 (Fall)</MenuItem>
                    <MenuItem value="2">Học kỳ 2 (Spring)</MenuItem>
                    <MenuItem value="3">Học kỳ 3 (Summer)</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid size={{ xs: 12 }}>
                <FormControl fullWidth>
                  <InputLabel>Lớp học</InputLabel>
                  <Select
                    value={state.classId}
                    onChange={(e) => handleClassChange(e.target.value)}
                    input={<OutlinedInput label="Lớp học" />}
                    disabled={!state.year}
                    renderValue={(v) =>
                      v ? classOptions.find((o) => o.value === v)?.label : 'Chọn lớp học'
                    }
                  >
                    <MenuItem value="">Chọn lớp học</MenuItem>
                    {classOptions.map((opt) => (
                      <MenuItem key={opt.value} value={opt.value}>
                        {opt.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid size={{ xs: 12, md: 6 }}>
                <FormControl fullWidth>
                  <DatePicker
                    label="Ngày điểm danh"
                    value={state.date ? dayjs(state.date) : null}
                    onChange={handleDateChange}
                    format="DD/MM/YYYY"
                  />
                </FormControl>
              </Grid>

              <Grid size={{ xs: 12, md: 6 }}>
                <TextField
                  fullWidth
                  label="Tiêu đề bài học"
                  value={state.content}
                  onChange={(e) => handleFieldChange('content', e.target.value)}
                  placeholder="Nhập nội dung bài học..."
                />
              </Grid>

              <Grid size={{ xs: 12 }}>
                <LoadingButton
                  variant="contained"
                  size="large"
                  loading={creating}
                  disabled={!canCreateAttendance}
                  onClick={handleCreateAttendance}
                  startIcon={<Iconify icon="mingcute:add-line" />}
                >
                  Tạo phiên điểm danh
                </LoadingButton>
              </Grid>
            </Grid>
          </Card>
        </Grid>
      </Grid>
    </DashboardContent>
  );
}
