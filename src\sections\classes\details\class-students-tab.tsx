'use client';

import type { StudentWithGrades } from 'src/types/academic';

import { useState, useEffect, useCallback } from 'react';

import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import CardContent from '@mui/material/CardContent';

import axios from 'src/lib/axios';
import { fetchAllClassStudents } from 'src/actions/ttu-student-adapter';
import { fetchAttendanceSessionsByClass } from 'src/actions/ttu-attendance-adapter';

import {
  useTable,
  emptyRows,
  TableNoData,
  TableEmptyRows,
  TableHeadCustom,
  TablePaginationCustom,
} from 'src/components/table';

import StudentTableRow from './student-table-row';
import StudentTableToolbar from './student-table-toolbar';

type AbsentData = {
  _id: number;
  is_permission: number;
  number_of_absence: number;
  student: {
    _id: number;
    first_name: string;
    last_name: string;
    email: string;
    avatar?: string;
  };
  attendance: {
    _id: number;
    year: number;
    semester: number;
    code: string;
    date: string;
    class: number;
    content: string;
  };
};

type StudentWithAttendance = StudentWithGrades & {
  totalAbsences?: number;
  hasPermission?: boolean;
  attendancePercentage?: number;
  attendanceStatus?: 'good' | 'warning' | 'critical';
};

type Props = { classId: string };

export default function ClassStudentsTab({ classId }: Props) {
  const table = useTable({ defaultRowsPerPage: 10 });
  const [rows, setRows] = useState<StudentWithAttendance[]>([]);
  const [students, setStudents] = useState<StudentWithGrades[]>([]);
  const [search, setSearch] = useState('');
  const [absentData, setAbsentData] = useState<Map<string, AbsentData>>(new Map());
  const [attendanceSessionsCount, setAttendanceSessionsCount] = useState(0);

  // Fetch absent data for the class
  const fetchAbsentData = useCallback(async () => {
    try {
      const response = await axios.get('/student/absent/list-absence', {
        params: { page: 1, perPage: -1, _class: classId },
      });

      const absentList: AbsentData[] = response.data?.data?.data || [];
      const absentMap = new Map<string, AbsentData>();

      absentList.forEach((item) => {
        const studentEmail = item.student.email;
        absentMap.set(studentEmail, item);
      });

      setAbsentData(absentMap);
    } catch (error) {
      console.error('Failed to fetch absent data:', error);
      setAbsentData(new Map());
    }
  }, [classId]);

  const loadStudents = useCallback(async () => {
    try {
      const studentList = await fetchAllClassStudents(classId);
      setStudents(studentList);
    } catch (error) {
      console.error('Failed to load students:', error);
    }
  }, [classId]);

  // Fetch attendance sessions count
  useEffect(() => {
    const getSessionsCount = async () => {
      try {
        const sessions = await fetchAttendanceSessionsByClass(classId);
        setAttendanceSessionsCount(sessions.length);
      } catch (error) {
        console.error('Failed to fetch attendance sessions:', error);
      }
    };
    getSessionsCount();
  }, [classId]);

  // Combine student data with absent data when either changes
  useEffect(() => {
    if (students.length > 0 && attendanceSessionsCount > 0) {
      const studentsWithAttendance: StudentWithAttendance[] = students.map((student) => {
        const absentInfo = absentData.get(student.email || '');
        const totalAbsences = absentInfo?.number_of_absence || 0;

        const attendancePercentage = Math.round(
          ((attendanceSessionsCount - totalAbsences) / attendanceSessionsCount) * 100
        );

        return {
          ...student,
          totalAbsences,
          hasPermission: absentInfo?.is_permission === 1,
          attendancePercentage,
          attendanceStatus: (attendancePercentage >= 80
            ? 'good'
            : attendancePercentage >= 60
              ? 'warning'
              : 'critical') as 'good' | 'warning' | 'critical',
        };
      });

      setRows(studentsWithAttendance);
    }
  }, [students, absentData, attendanceSessionsCount]);

  useEffect(() => {
    loadStudents();
    fetchAbsentData();
  }, [loadStudents, fetchAbsentData]);

  const filtered = search
    ? rows.filter(
        (s) =>
          s.studentId.toLowerCase().includes(search.toLowerCase()) ||
          s.fullName.toLowerCase().includes(search.toLowerCase())
      )
    : rows;

  return (
    <>
      <StudentTableToolbar search={search} onSearchChange={setSearch} />
      <CardContent sx={{ pt: 0 }}>
        <Table size={table.dense ? 'small' : 'medium'} sx={{ minWidth: 800 }}>
          <TableHeadCustom
            headCells={[
              { id: 'studentId', label: 'MSSV' },
              { id: 'fullName', label: 'Thông tin sinh viên' },
              { id: 'attendanceRate', label: 'Tỷ lệ điểm danh' },
              { id: 'attendance', label: 'Chi tiết vắng' },
            ]}
            order={table.order}
            orderBy={table.orderBy}
            rowCount={filtered.length}
            numSelected={0}
            onSort={table.onSort}
          />
          <TableBody>
            {filtered
              .slice(
                table.page * table.rowsPerPage,
                table.page * table.rowsPerPage + table.rowsPerPage
              )
              .map((row) => (
                <StudentTableRow key={row._id} row={row} />
              ))}
            <TableEmptyRows
              height={table.dense ? 52 : 72}
              emptyRows={emptyRows(table.page, table.rowsPerPage, filtered.length)}
            />
            <TableNoData notFound={!filtered.length} />
          </TableBody>
        </Table>
        <TablePaginationCustom
          count={filtered.length}
          page={table.page}
          rowsPerPage={table.rowsPerPage}
          onPageChange={table.onChangePage}
          onRowsPerPageChange={table.onChangeRowsPerPage}
          dense={table.dense}
          onChangeDense={table.onChangeDense}
        />
      </CardContent>
    </>
  );
}
