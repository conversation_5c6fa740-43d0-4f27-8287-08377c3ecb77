'use client';

import type { GradeComponent } from 'src/types/academic';

import dayjs from 'dayjs';
import { useState, useEffect, useCallback } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Chip from '@mui/material/Chip';
import Table from '@mui/material/Table';
import Alert from '@mui/material/Alert';
import Select from '@mui/material/Select';
import Button from '@mui/material/Button';
import TableRow from '@mui/material/TableRow';
import MenuItem from '@mui/material/MenuItem';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import Typography from '@mui/material/Typography';
import CardHeader from '@mui/material/CardHeader';
import InputLabel from '@mui/material/InputLabel';
import IconButton from '@mui/material/IconButton';
import FormControl from '@mui/material/FormControl';
import OutlinedInput from '@mui/material/OutlinedInput';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import CircularProgress from '@mui/material/CircularProgress';

import { paths } from 'src/routes/paths';
import { useRouter, useSearchParams } from 'src/routes/hooks';

import { useDragScroll } from 'src/hooks/use-drag-scroll';

import { getGradeComponents } from 'src/actions/ttu-api';
import {
  fetchClassOptions,
  getCurrentSemester,
  getCurrentAcademicYear,
} from 'src/actions/ttu-report-adapter';

import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';

// ----------------------------------------------------------------------

const TABLE_HEAD = [
  { id: 'name_vie', label: 'Tên thành phần' },
  { id: 'name_eng', label: 'Tên tiếng Anh' },
  { id: 'weight', label: 'Trọng số (%)' },
  { id: 'description', label: 'Mô tả' },
  { id: 'created_by', label: 'Người tạo' },
  { id: 'actions', label: 'Thao tác', width: 180 },
];

type Props = {
  onClassSelect: (classId: number, classData?: any) => void;
  selectedClass: number | null;
  refreshKey?: number;
  onEditComponent?: (component: GradeComponent) => void;
};

export function GradebookTable({
  onClassSelect,
  selectedClass,
  refreshKey,
  onEditComponent,
}: Props) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [classOptions, setClassOptions] = useState<Array<{ value: string; label: string }>>([]);
  const [gradeComponents, setGradeComponents] = useState<GradeComponent[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [year, setYear] = useState<string>(getCurrentAcademicYear());
  const [semester, setSemester] = useState<string>(getCurrentSemester());

  // Auto-set year and semester from URL parameters
  useEffect(() => {
    const urlYear = searchParams.get('year');
    const urlSemester = searchParams.get('semester');

    if (urlYear && urlYear !== year) {
      setYear(urlYear);
    }
    if (urlSemester && urlSemester !== semester) {
      setSemester(urlSemester);
    }
  }, [searchParams, year, semester]);

  // Drag scroll for table
  const dragScroll = useDragScroll({
    scrollSpeed: 2,
    disabled: false,
  });

  // Load classes when year/semester changes
  useEffect(() => {
    const loadClasses = async () => {
      if (year) {
        try {
          setLoading(true);
          const opts = await fetchClassOptions(year, semester);
          setClassOptions(opts);
          setError(null);
        } catch (err) {
          console.error('Failed to load classes:', err);
          setError('Không thể tải danh sách lớp học');
        } finally {
          setLoading(false);
        }
      } else {
        setClassOptions([]);
      }
    };

    loadClasses();
  }, [year, semester]);

  // Force load classes when URL params are present (to handle async timing)
  useEffect(() => {
    const urlYear = searchParams.get('year');
    const urlSemester = searchParams.get('semester');
    const urlClassId = searchParams.get('classId');

    if (urlYear && urlSemester && urlClassId && classOptions.length === 0) {
      // Force reload classes if we have URL params but no options loaded yet
      const loadClassesForUrl = async () => {
        try {
          setLoading(true);
          const opts = await fetchClassOptions(urlYear, urlSemester);
          setClassOptions(opts);
          setError(null);
        } catch (err) {
          console.error('Failed to load classes for URL params:', err);
          setError('Không thể tải danh sách lớp học');
        } finally {
          setLoading(false);
        }
      };

      loadClassesForUrl();
    }
  }, [searchParams, classOptions.length]);

  // Fetch grade components for selected class
  const fetchGradeComponents = useCallback(
    async (classId: number) => {
      try {
        setLoading(true);
        const response = await getGradeComponents({
          year: parseInt(year, 10),
          semester: parseInt(semester, 10),
          _class: classId,
          page: 1,
          perPage: -1,
        });
        const components = response?.data?.data?.data || [];
        setGradeComponents(components);
        setError(null);
      } catch (err) {
        console.error('Error fetching grade components:', err);
        setError('Không thể tải thành phần điểm');
      } finally {
        setLoading(false);
      }
    },
    [year, semester]
  );

  useEffect(() => {
    if (selectedClass) {
      fetchGradeComponents(selectedClass);
    } else {
      setGradeComponents([]);
    }
  }, [selectedClass, fetchGradeComponents, refreshKey]);

  const handleYearChange = (val: any) => {
    const yearValue = val ? String(val.year()) : '';
    setYear(yearValue);
    onClassSelect(0); // Reset class when year changes
  };

  const handleSemesterChange = (value: string) => {
    setSemester(value);
    onClassSelect(0); // Reset class when semester changes
  };

  const handleClassChange = useCallback(
    (classId: number) => {
      // Find class data from options to get additional info
      const classOption = classOptions.find((opt) => opt.value === String(classId));
      // Create a mock class data object for compatibility
      const classData = classOption
        ? {
            _id: classId,
            year: parseInt(year, 10),
            semester: parseInt(semester, 10),
            course_id: 1271, // Default course ID
            class_code: classOption.label.split(' - ')[1] || '',
            class_name: classOption.label.split(' - ')[0] || '',
          }
        : null;
      onClassSelect(classId, classData);
    },
    [onClassSelect, classOptions, year, semester]
  );

  const handleGradeEntry = useCallback(
    (component: GradeComponent) => {
      // Navigate to grade entry page and auto-select this component
      router.push(`${paths.dashboard.gradeEntry}?componentId=${component._id}`);
    },
    [router]
  );

  const totalWeight = gradeComponents.reduce((sum, component) => sum + component.weight, 0);

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
      {/* Class Selection */}
      <Card>
        <CardHeader title="Chọn lớp học" />
        <Box sx={{ p: 3, display: 'flex', gap: 2, alignItems: 'center', flexWrap: 'wrap' }}>
          <FormControl sx={{ minWidth: 200 }}>
            <DatePicker
              views={['year']}
              label="Năm học"
              value={year ? dayjs(`${year}-01-01`) : null}
              onChange={handleYearChange}
            />
          </FormControl>

          <FormControl sx={{ minWidth: 150 }}>
            <InputLabel>Học kỳ</InputLabel>
            <Select
              value={semester}
              onChange={(e) => handleSemesterChange(e.target.value)}
              input={<OutlinedInput label="Học kỳ" />}
              displayEmpty
            >
              <MenuItem value="1">Học kỳ 1 (Fall)</MenuItem>
              <MenuItem value="2">Học kỳ 2 (Spring)</MenuItem>
              <MenuItem value="3">Học kỳ 3 (Summer)</MenuItem>
            </Select>
          </FormControl>

          <FormControl sx={{ minWidth: 300 }}>
            <InputLabel>Lớp học</InputLabel>
            <Select
              value={selectedClass || ''}
              onChange={(e) => handleClassChange(Number(e.target.value))}
              input={<OutlinedInput label="Lớp học" />}
              disabled={!year || loading}
              renderValue={(v) =>
                v ? classOptions.find((o) => o.value === String(v))?.label : 'Chọn lớp học'
              }
            >
              <MenuItem value="">Chọn lớp học</MenuItem>
              {classOptions.map((opt) => (
                <MenuItem key={opt.value} value={Number(opt.value)}>
                  {opt.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {loading && <CircularProgress size={24} />}
        </Box>
      </Card>

      {/* Grade Components Table */}
      {selectedClass !== null && (
        <Card>
          <CardHeader
            sx={{
              mb: 3,
            }}
            title="Thành phần điểm"
            subheader={`Tổng trọng số: ${totalWeight}%`}
            action={
              totalWeight !== 100 &&
              gradeComponents.length > 0 && (
                <Chip label={`Thiếu ${100 - totalWeight}%`} color="warning" size="small" />
              )
            }
          />

          {error && (
            <Alert severity="error" sx={{ m: 2 }}>
              {error}
            </Alert>
          )}

          {gradeComponents.length === 0 && !loading ? (
            <Box sx={{ p: 3, textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                Chưa có thành phần điểm nào. Nhấn &quot;Thêm thành phần điểm&quot; để tạo mới.
              </Typography>
            </Box>
          ) : (
            <Scrollbar
              ref={dragScroll.ref}
              {...dragScroll.handlers}
              sx={{
                cursor: 'grab',
                '&:active': {
                  cursor: 'grabbing',
                },
              }}
            >
              <Table size="small">
                <TableHead>
                  <TableRow>
                    {TABLE_HEAD.map((headCell) => (
                      <TableCell key={headCell.id} sx={{ width: headCell.width }}>
                        {headCell.label}
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {gradeComponents.map((component) => (
                    <TableRow key={component._id}>
                      <TableCell>{component.name_vie}</TableCell>
                      <TableCell>{component.name_eng}</TableCell>
                      <TableCell>
                        <Chip
                          label={`${component.weight}%`}
                          size="small"
                          color={component.weight > 0 ? 'primary' : 'default'}
                        />
                      </TableCell>
                      <TableCell>{component.description || '-'}</TableCell>
                      <TableCell>{component.created_by}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 0.5 }}>
                          <IconButton
                            size="small"
                            onClick={() => onEditComponent?.(component)}
                            color="primary"
                            title="Sửa thành phần điểm"
                          >
                            <Iconify icon="solar:pen-bold" />
                          </IconButton>
                          <Button
                            size="small"
                            variant="contained"
                            onClick={() => handleGradeEntry(component)}
                            startIcon={<Iconify icon="solar:calculator-bold" />}
                            sx={{ minWidth: 'auto', px: 1 }}
                          >
                            Nhập điểm
                          </Button>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Scrollbar>
          )}
        </Card>
      )}
    </Box>
  );
}
