'use client';

import type { StudentWithGrades } from 'src/types/academic';

import Chip from '@mui/material/Chip';
import Stack from '@mui/material/Stack';
import Avatar from '@mui/material/Avatar';
import TableRow from '@mui/material/TableRow';
import TableCell from '@mui/material/TableCell';
import Typography from '@mui/material/Typography';
import LinearProgress from '@mui/material/LinearProgress';

type StudentWithAttendance = StudentWithGrades & {
  totalAbsences?: number;
  hasPermission?: boolean;
  attendancePercentage?: number;
  attendanceStatus?: 'good' | 'warning' | 'critical';
};

type Props = {
  row: StudentWithAttendance;
};

export default function StudentTableRow({ row }: Props) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'warning';
      case 'graduated':
        return 'info';
      case 'dropped':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <TableRow>
      <TableCell sx={{ whiteSpace: 'nowrap' }}>{row.studentId}</TableCell>
      <TableCell>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Avatar src={row.avatar} alt={row.fullName} sx={{ width: 40, height: 40 }}>
            {row.fullName.charAt(0)}
          </Avatar>
          <div>
            <Typography variant="subtitle2" noWrap>
              {row.fullName}
            </Typography>
            {row.email && (
              <Typography variant="body2" color="text.secondary" noWrap>
                {row.email}
              </Typography>
            )}
          </div>
        </Stack>
      </TableCell>
      <TableCell>
        {row.attendancePercentage !== undefined ? (
          <Stack spacing={1} alignItems="center">
            <Typography variant="body2">{`${row.attendancePercentage}%`}</Typography>
            <LinearProgress
              variant="determinate"
              value={row.attendancePercentage}
              color={
                row.attendanceStatus === 'good'
                  ? 'success'
                  : row.attendanceStatus === 'warning'
                    ? 'warning'
                    : 'error'
              }
              sx={{ width: 1, height: 6, borderRadius: 3 }}
            />
          </Stack>
        ) : (
          '-'
        )}
      </TableCell>
      <TableCell>
        <Stack direction="row" spacing={1} alignItems="center">
          <Chip
            size="small"
            label={`${row.totalAbsences || 0} vắng`}
            color={
              row.attendanceStatus === 'good'
                ? 'success'
                : row.attendanceStatus === 'warning'
                  ? 'warning'
                  : 'error'
            }
            variant="soft"
          />
          {row.hasPermission && <Chip size="small" label="Có phép" color="info" variant="soft" />}
        </Stack>
      </TableCell>
    </TableRow>
  );
}
