import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Chip from '@mui/material/Chip';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Avatar from '@mui/material/Avatar';
import Typography from '@mui/material/Typography';
import CardActions from '@mui/material/CardActions';
import CardContent from '@mui/material/CardContent';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { fDate } from 'src/utils/format-time';
import { fPercent } from 'src/utils/format-number';

import { Label } from 'src/components/label';
import { Iconify } from 'src/components/iconify';

import type { ClassWithStats } from 'src/types/academic';

// ----------------------------------------------------------------------

type Props = {
  classData: ClassWithStats;
  sx?: any;
};

export function ClassCard({ classData, sx }: Props) {
  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'warning';
      case 'completed':
        return 'default';
      default:
        return 'default';
    }
  };

  const getStatusLabel = (status?: string) => {
    switch (status) {
      case 'active':
        return 'Đang hoạt động';
      case 'inactive':
        return 'Tạm dừng';
      case 'completed':
        return 'Đã hoàn thành';
      default:
        return 'Không xác định';
    }
  };

  return (
    <Card
      sx={[
        { height: '100%', display: 'flex', flexDirection: 'column' },
        ...(Array.isArray(sx) ? sx : [sx]),
      ]}
    >
      <CardContent sx={{ flexGrow: 1 }}>
        <Stack direction="row" spacing={2} alignItems="flex-start" sx={{ mb: 2 }}>
          <Avatar
            sx={{
              bgcolor: 'primary.lighter',
              color: 'primary.main',
              width: 48,
              height: 48,
            }}
          >
            <Iconify icon="solar:book-2-bold-duotone" width={24} />
          </Avatar>

          <Box sx={{ flexGrow: 1, minWidth: 0 }}>
            <Typography variant="h6" noWrap sx={{ mb: 0.5 }}>
              {classData.code}
            </Typography>
            <Label color={getStatusColor(classData.status)} variant="soft">
              {getStatusLabel(classData.status)}
            </Label>
          </Box>
        </Stack>

        <Typography variant="body1" sx={{ mb: 2, fontWeight: 500 }}>
          {classData.name}
        </Typography>

        <Stack spacing={1} sx={{ mb: 2 }}>
          <Stack direction="row" alignItems="center" spacing={1}>
            <Iconify icon="solar:calendar-linear" width={16} sx={{ color: 'text.secondary' }} />
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              {classData.term}
            </Typography>
          </Stack>

          <Stack direction="row" alignItems="center" spacing={1}>
            <Iconify
              icon="solar:users-group-rounded-linear"
              width={16}
              sx={{ color: 'text.secondary' }}
            />
            <Typography variant="body2" sx={{ color: 'text.secondary' }}>
              {classData.totalStudents} sinh viên
            </Typography>
          </Stack>

          {classData.attendanceRate && (
            <Stack direction="row" alignItems="center" spacing={1}>
              <Iconify icon="solar:chart-2-linear" width={16} sx={{ color: 'text.secondary' }} />
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Chuyên cần: {fPercent(classData.attendanceRate)}
              </Typography>
            </Stack>
          )}

          {classData.nextSessionAt && (
            <Stack direction="row" alignItems="center" spacing={1}>
              <Iconify
                icon="solar:clock-circle-linear"
                width={16}
                sx={{ color: 'text.secondary' }}
              />
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                Buổi tiếp theo: {fDate(classData.nextSessionAt)}
              </Typography>
            </Stack>
          )}
        </Stack>

        {classData.schedule && (
          <Chip size="small" label={classData.schedule} variant="outlined" sx={{ mb: 2 }} />
        )}
      </CardContent>

      <CardActions sx={{ p: 2, pt: 0 }}>
        <Stack direction="row" spacing={1} sx={{ width: '100%' }}>
          <Button
            component={RouterLink}
            href={paths.dashboard.classes.details(classData.id)}
            size="small"
            variant="outlined"
            fullWidth
          >
            Chi tiết
          </Button>
          <Button
            component={RouterLink}
            href={paths.dashboard.classes.attendance(classData.id)}
            size="small"
            variant="contained"
            fullWidth
            startIcon={<Iconify icon="solar:user-check-linear" />}
          >
            Điểm danh
          </Button>
        </Stack>
      </CardActions>
    </Card>
  );
}
