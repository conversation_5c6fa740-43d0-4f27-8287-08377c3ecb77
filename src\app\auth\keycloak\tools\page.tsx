'use client';

import { Box, Typography, Paper, Button, Grid, Card, CardContent, CardActions } from '@mui/material';

// ----------------------------------------------------------------------

type ToolItem = {
  title: string;
  description: string;
  icon: string;
  url: string;
  color: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success';
};

type ExternalLink = {
  title: string;
  description: string;
  icon: string;
  url: string;
  color: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success';
};

type DocumentationItem = {
  title: string;
  description: string;
  file: string;
};

// ----------------------------------------------------------------------

const tools: ToolItem[] = [
  {
    title: 'Config Check',
    description: 'Kiểm tra environment variables và validate cấu hình',
    icon: '⚙️',
    url: '/auth/keycloak/config-check',
    color: 'primary'
  },
  {
    title: 'Test Logout URL',
    description: 'Test trực tiếp logout redirect URI để xác định vấn đề',
    icon: '🧪',
    url: '/auth/keycloak/test-logout',
    color: 'warning'
  },
  {
    title: 'Debug Panel',
    description: 'Hiển thị thông tin user và test logout flow',
    icon: '🐛',
    url: '/auth/keycloak/debug',
    color: 'info'
  },
  {
    title: 'Client Check',
    description: 'Kiểm tra cấu hình Keycloak client qua API (cần admin token)',
    icon: '✅',
    url: '/auth/keycloak/client-check',
    color: 'success'
  }
];

const externalLinks: ExternalLink[] = [
  {
    title: 'Keycloak Admin Console',
    description: 'Truy cập Keycloak Admin để cấu hình client',
    icon: '🔧',
    url: 'http://localhost:8080/admin',
    color: 'error'
  }
];

const documentation: DocumentationItem[] = [
  {
    title: 'Step-by-Step Fix Guide',
    description: 'Hướng dẫn từng bước khắc phục lỗi logout',
    file: 'KEYCLOAK_STEP_BY_STEP_FIX.md'
  },
  {
    title: 'Logout Fix Documentation',
    description: 'Tài liệu chi tiết về lỗi logout và cách khắc phục',
    file: 'KEYCLOAK_LOGOUT_FIX.md'
  },
  {
    title: 'Implementation Overview',
    description: 'Tổng quan về implementation Keycloak',
    file: 'KEYCLOAK_IMPLEMENTATION.md'
  }
];

export default function KeycloakToolsPage() {
  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        Keycloak Debug Tools
      </Typography>

      <Typography variant="body1" color="text.secondary" paragraph>
        Tập hợp các công cụ để debug và khắc phục vấn đề Keycloak logout.
      </Typography>

      {/* Debug Tools */}
      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }}>
        Debug Tools
      </Typography>

      <Grid container spacing={3}>
        {tools.map((tool, index) => (
          <Grid item xs={12} md={6} key={index}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <CardContent sx={{ flexGrow: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h4" sx={{ mr: 1 }}>
                    {tool.icon}
                  </Typography>
                  <Typography variant="h6">
                    {tool.title}
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  {tool.description}
                </Typography>
              </CardContent>
              <CardActions>
                <Button
                  variant="contained"
                  color={tool.color}
                  onClick={() => window.open(tool.url, '_blank')}
                  fullWidth
                >
                  Open Tool
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* External Links */}
      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }}>
        External Tools
      </Typography>

      <Grid container spacing={3}>
        {externalLinks.map((link, index) => (
          <Grid item xs={12} md={6} key={index}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <CardContent sx={{ flexGrow: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h4" sx={{ mr: 1 }}>
                    {link.icon}
                  </Typography>
                  <Typography variant="h6">
                    {link.title}
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  {link.description}
                </Typography>
              </CardContent>
              <CardActions>
                <Button
                  variant="contained"
                  color={link.color}
                  onClick={() => window.open(link.url, '_blank')}
                  fullWidth
                >
                  Open External
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Documentation */}
      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 2 }}>
        Documentation
      </Typography>

      <Grid container spacing={3}>
        {documentation.map((doc, index) => (
          <Grid item xs={12} md={4} key={index}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <CardContent sx={{ flexGrow: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h4" sx={{ mr: 1 }}>
                    📄
                  </Typography>
                  <Typography variant="h6">
                    {doc.title}
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  {doc.description}
                </Typography>
                <Typography variant="caption" color="text.disabled" sx={{ mt: 1, display: 'block' }}>
                  File: {doc.file}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Quick Start Guide */}
      <Paper sx={{ p: 3, mt: 4, bgcolor: 'primary.light', color: 'primary.contrastText' }}>
        <Typography variant="h6" gutterBottom>
          🚀 Quick Start - Khắc phục lỗi &quot;Invalid redirect uri&quot;
        </Typography>
        <Box component="ol" sx={{ pl: 2 }}>
          <li>
            <Typography variant="body2">
              <strong>Test vấn đề:</strong> Sử dụng &quot;Test Logout URL&quot; để xác nhận lỗi
            </Typography>
          </li>
          <li>
            <Typography variant="body2">
              <strong>Kiểm tra config:</strong> Sử dụng &quot;Config Check&quot; để xem cấu hình hiện tại
            </Typography>
          </li>
          <li>
            <Typography variant="body2">
              <strong>Cấu hình Keycloak:</strong> Mở &quot;Keycloak Admin Console&quot; và thêm Valid post logout redirect URIs
            </Typography>
          </li>
          <li>
            <Typography variant="body2">
              <strong>Test lại:</strong> Sử dụng &quot;Debug Panel&quot; để test logout flow hoàn chỉnh
            </Typography>
          </li>
        </Box>
      </Paper>

      {/* Current Issue Status */}
      <Paper sx={{ p: 3, mt: 3, bgcolor: 'warning.light', color: 'warning.contrastText' }}>
        <Typography variant="h6" gutterBottom>
          ⚠️ Current Issue
        </Typography>
        <Typography variant="body2" paragraph>
          Logout đang bị kẹt tại Keycloak với lỗi &quot;Invalid redirect uri&quot;. Nguyên nhân chính là thiếu cấu hình
          &quot;Valid post logout redirect URIs&quot; trong Keycloak client.
        </Typography>
        <Typography variant="body2">
          <strong>URL bị lỗi:</strong> <code>http://localhost:8080/realms/ttu/protocol/openid-connect/logout?...</code>
        </Typography>
      </Paper>
    </Box>
  );
}
