import Stack from '@mui/material/Stack';
import TextField from '@mui/material/TextField';
import FormControl from '@mui/material/FormControl';
import OutlinedInput from '@mui/material/OutlinedInput';
import MenuItem from '@mui/material/MenuItem';
import Select from '@mui/material/Select';
import dayjs from 'dayjs';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';

export type ReportFiltersState = {
  year?: string;
  semester?: string;
  classId?: string;
};

type Props = {
  filters: ReportFiltersState;
  onChange: (name: keyof ReportFiltersState, value: string) => void;
  classOptions: Array<{ value: string; label: string }>;
};

export function ReportFilters({ filters, onChange, classOptions }: Props) {
  return (
    <Stack spacing={2} direction={{ xs: 'column', md: 'row' }} sx={{ p: 2 }}>
      <FormControl sx={{ width: { xs: 1, md: 180 } }}>
        <DatePicker
          views={['year']}
          label="Năm học"
          value={filters.year ? dayjs(`${filters.year}-01-01`) : null}
          onChange={(val) => onChange('year', val ? String(val.year()) : '')}
          slotProps={{ textField: { fullWidth: true } }}
        />
      </FormControl>

      <FormControl sx={{ width: { xs: 1, md: 180 } }}>
        <Select
          value={filters.semester || ''}
          onChange={(e) => onChange('semester', e.target.value)}
          input={<OutlinedInput label="Học kỳ" />}
          displayEmpty
          renderValue={(v) => {
            if (!v) return 'Chọn học kỳ';
            const semesterMap: Record<string, string> = {
              '1': 'Học kỳ 1 (Fall)',
              '2': 'Học kỳ 2 (Spring)',
              '3': 'Học kỳ 3 (Summer)',
            };
            return semesterMap[v] || v;
          }}
        >
          <MenuItem value="">Tất cả học kỳ</MenuItem>
          <MenuItem value="1">Học kỳ 1 (Fall)</MenuItem>
          <MenuItem value="2">Học kỳ 2 (Spring)</MenuItem>
          <MenuItem value="3">Học kỳ 3 (Summer)</MenuItem>
        </Select>
      </FormControl>

      <FormControl sx={{ width: { xs: 1, md: 320 } }}>
        <Select
          value={filters.classId || ''}
          onChange={(e) => onChange('classId', e.target.value)}
          input={<OutlinedInput label="Lớp học" />}
          displayEmpty
          renderValue={(v) => (v ? classOptions.find((o) => o.value === v)?.label : 'Chọn lớp học')}
          MenuProps={{ PaperProps: { sx: { maxHeight: 300 } } }}
          disabled={!filters.year}
        >
          <MenuItem value="">Tất cả lớp</MenuItem>
          {classOptions.map((opt) => (
            <MenuItem key={opt.value} value={opt.value}>
              {opt.label}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </Stack>
  );
}
