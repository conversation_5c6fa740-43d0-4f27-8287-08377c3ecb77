'use client';

import { useState, useEffect, useCallback } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Chip from '@mui/material/Chip';
import Table from '@mui/material/Table';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import Tooltip from '@mui/material/Tooltip';
import TableRow from '@mui/material/TableRow';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import DialogContentText from '@mui/material/DialogContentText';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { DashboardContent } from 'src/layouts/dashboard';
import { filterAttendance, deleteAttendance } from 'src/actions/ttu-api';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';
import { TablePaginationCustom } from 'src/components/table';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';

// Type for attendance session
type AttendanceSession = {
  _id: number;
  year: number;
  semester: number;
  content: string;
  date: string;
  code: string;
  created_by: string;
  created_at: string;
  lecturer: {
    _id: number;
    first_name: string;
    last_name: string;
    email: string;
  };
  class: {
    _id: number;
    name: string;
    name_vn: string;
  };
};

type Props = {
  classId: string;
};

export default function AttendanceView({ classId }: Props) {
  const [items, setItems] = useState<AttendanceSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<AttendanceSession | null>(null);
  const [deleting, setDeleting] = useState(false);

  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      const response = await filterAttendance({
        _class: classId,
        page: page + 1,
        perPage: rowsPerPage,
      });

      const data = response?.data?.data?.data || [];
      const totalCount = response?.data?.data?.total || 0;

      setItems(data);
      setTotal(totalCount);
    } catch (error) {
      console.error('Error loading attendance sessions:', error);
      toast.error('Có lỗi xảy ra khi tải danh sách phiên điểm danh');
    } finally {
      setLoading(false);
    }
  }, [classId, page, rowsPerPage]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  const handlePageChange = (_event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleRowsPerPageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleDeleteClick = (item: AttendanceSession) => {
    setSelectedItem(item);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedItem) return;

    try {
      setDeleting(true);
      await deleteAttendance(selectedItem._id.toString());
      toast.success('Đã xóa phiên điểm danh thành công');
      setDeleteDialogOpen(false);
      setSelectedItem(null);
      loadData(); // Reload data
    } catch (error) {
      console.error('Error deleting attendance session:', error);
      toast.error('Có lỗi xảy ra khi xóa phiên điểm danh');
    } finally {
      setDeleting(false);
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setSelectedItem(null);
  };

  const handleCreateSession = () => {
    // Navigate to create attendance page with classId pre-filled
    window.location.href = `${paths.dashboard.attendance}?classId=${classId}`;
  };

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="Lịch sử điểm danh"
        links={[
          { name: 'Dashboard', href: paths.dashboard.root },
          { name: 'Lớp học', href: paths.dashboard.classes.root },
          { name: 'Lịch sử điểm danh' },
        ]}
        action={
          <Button
            variant="contained"
            startIcon={<Iconify icon="mingcute:add-line" />}
            onClick={handleCreateSession}
          >
            Tạo phiên điểm danh
          </Button>
        }
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card>
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Danh sách phiên điểm danh
          </Typography>
        </Box>

        <Scrollbar>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>Thông tin phiên</TableCell>
                <TableCell>Lớp học</TableCell>
                <TableCell>Giảng viên</TableCell>
                <TableCell>Thời gian</TableCell>
                <TableCell>Trạng thái</TableCell>
                <TableCell align="right">Hành động</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    <Typography variant="body2" color="text.secondary">
                      Đang tải...
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : items?.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    <Typography variant="body2" color="text.secondary">
                      Chưa có phiên điểm danh nào
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                items?.map((item) => (
                  <TableRow key={item._id} hover>
                    <TableCell>
                      <Box>
                        <Typography variant="subtitle2" noWrap>
                          {item.content || 'Không có tiêu đề'}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" noWrap>
                          {new Date(item.date).toLocaleDateString('vi-VN')}
                        </Typography>
                        <Typography variant="caption" color="text.secondary" noWrap>
                          ID: {item._id}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" noWrap>
                          {item.class?.name_vn || item.class?.name}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {item.code}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" noWrap>
                          {item.lecturer
                            ? `${item.lecturer.last_name} ${item.lecturer.first_name}`
                            : 'Chưa có thông tin'}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {item.created_by}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2">
                          {`Học kỳ ${item.semester} - ${item.year}`}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Tạo: {new Date(item.created_at).toLocaleDateString('vi-VN')}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip size="small" label="Hoàn thành" color="success" variant="outlined" />
                    </TableCell>
                    <TableCell align="right">
                      <Stack direction="row" spacing={0.5}>
                        <Tooltip title="Xem chi tiết">
                          <IconButton
                            component={RouterLink}
                            href={paths.dashboard.classes.attendanceSession(
                              classId,
                              item._id.toString()
                            )}
                            color="primary"
                            size="small"
                          >
                            <Iconify icon="solar:eye-bold" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Xóa">
                          <IconButton
                            onClick={() => handleDeleteClick(item)}
                            color="error"
                            size="small"
                          >
                            <Iconify icon="solar:trash-bin-trash-bold" />
                          </IconButton>
                        </Tooltip>
                      </Stack>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </Scrollbar>

        <TablePaginationCustom
          count={total}
          page={page}
          rowsPerPage={rowsPerPage}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleRowsPerPageChange}
          dense
        />
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={handleDeleteCancel}>
        <DialogTitle>Xác nhận xóa</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Bạn có chắc chắn muốn xóa phiên điểm danh &quot;{selectedItem?.content}&quot; không?
            Hành động này không thể hoàn tác.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} disabled={deleting}>
            Hủy
          </Button>
          <Button onClick={handleDeleteConfirm} color="error" disabled={deleting}>
            {deleting ? 'Đang xóa...' : 'Xóa'}
          </Button>
        </DialogActions>
      </Dialog>
    </DashboardContent>
  );
}
