'use client';

import dayjs from 'dayjs';
import { useState, useEffect, useCallback } from 'react';

import Card from '@mui/material/Card';
import Chip from '@mui/material/Chip';
import Grid from '@mui/material/Grid2';
import Table from '@mui/material/Table';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Select from '@mui/material/Select';
import Dialog from '@mui/material/Dialog';
import Tooltip from '@mui/material/Tooltip';
import TableRow from '@mui/material/TableRow';
import MenuItem from '@mui/material/MenuItem';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import InputLabel from '@mui/material/InputLabel';
import FormControl from '@mui/material/FormControl';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';

import { paths } from 'src/routes/paths';

import { DashboardContent } from 'src/layouts/dashboard';
import { getCalendar, postCalendar, deleteCalendar } from 'src/actions/ttu-api';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';
import { ConfirmDialog } from 'src/components/custom-dialog';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';

type Semester = {
  id: string;
  year: number;
  semester: number;
  startDate: string;
  endDate: string;
  createdAt: string;
};

type CreateSemesterData = {
  year: string;
  semester: string;
  startDate: dayjs.Dayjs | null;
  endDate: dayjs.Dayjs | null;
};

export default function SemestersView() {
  const [semesters, setSemesters] = useState<Semester[]>([]);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [openConfirm, setOpenConfirm] = useState(false);
  const [selectedSemester, setSelectedSemester] = useState<Semester | null>(null);

  const [formData, setFormData] = useState<CreateSemesterData>({
    year: '',
    semester: '',
    startDate: null,
    endDate: null,
  });

  // Load semesters
  const loadSemesters = useCallback(async () => {
    try {
      setLoading(true);
      const result = await getCalendar(1, -1); // Get all semesters
      console.log('Calendar API result:', result?.data);
      const data = result?.data?.data?.docs || result?.data?.data || [];
      console.log('Calendar data:', data);

      const mappedSemesters: Semester[] = data.map((item: any) => ({
        id: item._id?.toString() || '',
        year: Number(item.year) || 0,
        semester: Number(item.semester) || 0,
        startDate: item.start_at || '',
        endDate: item.end_at || '',
        createdAt: item.created_at || '',
      }));

      setSemesters(mappedSemesters);
      console.log('Mapped semesters:', mappedSemesters);
    } catch (error) {
      console.error('Failed to load semesters:', error);
      toast.error('Có lỗi xảy ra khi tải danh sách học kỳ');
      setSemesters([]); // Set empty array on error
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadSemesters();
  }, [loadSemesters]);

  // Test function to debug API
  const testAPI = async () => {
    try {
      console.log('Testing calendar API...');
      const result = await getCalendar(1, -1);
      console.log('Raw API response:', result);
    } catch (error) {
      console.error('API test error:', error);
    }
  };

  // Call test on mount (remove this after debugging)
  useEffect(() => {
    testAPI();
  }, []);

  const handleOpenDialog = () => {
    setFormData({
      year: '',
      semester: '',
      startDate: null,
      endDate: null,
    });
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleFormChange = (field: keyof CreateSemesterData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleCreateSemester = async () => {
    // Validation
    if (!formData.year) {
      toast.warning('Vui lòng chọn năm học');
      return;
    }
    if (!formData.semester) {
      toast.warning('Vui lòng chọn học kỳ');
      return;
    }
    if (!formData.startDate || !formData.endDate) {
      toast.warning('Vui lòng chọn ngày bắt đầu và kết thúc');
      return;
    }

    try {
      const data = {
        year: formData.year,
        semester: formData.semester,
        start_at: formData.startDate.format('YYYY-MM-DD'),
        end_at: formData.endDate.format('YYYY-MM-DD'),
      };

      await postCalendar(data);
      toast.success('Tạo học kỳ thành công');
      handleCloseDialog();
      loadSemesters();
    } catch (error: any) {
      console.error('Failed to create semester:', error);
      const errorMessage = error?.response?.data?.message || 'Có lỗi xảy ra khi tạo học kỳ';
      toast.error(errorMessage);
    }
  };

  const handleDeleteSemester = async () => {
    if (!selectedSemester) return;

    try {
      await deleteCalendar(selectedSemester.id);
      toast.success('Xóa học kỳ thành công');
      setOpenConfirm(false);
      setSelectedSemester(null);
      loadSemesters();
    } catch (error: any) {
      console.error('Failed to delete semester:', error);
      const errorMessage = error?.response?.data?.message || 'Có lỗi xảy ra khi xóa học kỳ';
      toast.error(errorMessage);
    }
  };

  const getSemesterLabel = (semester: number) => {
    switch (semester) {
      case 1:
        return 'Fall';
      case 2:
        return 'Spring';
      case 3:
        return 'Summer';
      default:
        return `Học kỳ ${semester}`;
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return dayjs(dateString).format('DD/MM/YYYY');
    } catch {
      return dateString;
    }
  };

  const generateYearOptions = () => {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let i = currentYear - 2; i <= currentYear + 5; i++) {
      years.push(i);
    }
    return years;
  };

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="Quản lý học kỳ"
        links={[{ name: 'Trang chủ', href: paths.dashboard.root }, { name: 'Quản lý học kỳ' }]}
        action={
          <Button
            variant="contained"
            startIcon={<Iconify icon="mingcute:add-line" />}
            onClick={handleOpenDialog}
          >
            Tạo học kỳ
          </Button>
        }
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card>
        <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ p: 3 }}>
          <Typography variant="h6">Danh sách học kỳ</Typography>
          <Typography variant="body2" color="text.secondary">
            {loading ? 'Đang tải...' : `Tổng: ${semesters.length} học kỳ`}
          </Typography>
        </Stack>

        <Scrollbar>
          <Table size="small" sx={{ minWidth: 800 }}>
            <TableHead>
              <TableRow>
                <TableCell>Năm học</TableCell>
                <TableCell>Học kỳ</TableCell>
                <TableCell>Thời gian</TableCell>
                <TableCell>Trạng thái</TableCell>
                <TableCell align="right">Hành động</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={5} align="center">
                    <Typography variant="body2" color="text.secondary">
                      Đang tải...
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : semesters.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} align="center">
                    <Typography variant="body2" color="text.secondary">
                      Chưa có học kỳ nào
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                semesters.map((semester) => {
                  const now = dayjs();
                  const start = dayjs(semester.startDate);
                  const end = dayjs(semester.endDate);
                  const isActive = now.isAfter(start) && now.isBefore(end);
                  const isUpcoming = now.isBefore(start);

                  return (
                    <TableRow key={semester.id} hover>
                      <TableCell>
                        <Typography variant="body2" fontWeight="medium">
                          {semester.year}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {getSemesterLabel(semester.semester)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {formatDate(semester.startDate)} - {formatDate(semester.endDate)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={isActive ? 'Đang diễn ra' : isUpcoming ? 'Sắp tới' : 'Đã kết thúc'}
                          color={isActive ? 'success' : isUpcoming ? 'warning' : 'default'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell align="right">
                        <Tooltip title="Xóa học kỳ">
                          <IconButton
                            color="error"
                            size="small"
                            onClick={() => {
                              setSelectedSemester(semester);
                              setOpenConfirm(true);
                            }}
                          >
                            <Iconify icon="solar:trash-bin-trash-bold" />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </Scrollbar>
      </Card>

      {/* Create Semester Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>Tạo học kỳ mới</DialogTitle>
        <DialogContent>
          <Stack spacing={3} sx={{ mt: 1 }}>
            <FormControl fullWidth>
              <InputLabel>Năm học</InputLabel>
              <Select
                value={formData.year}
                onChange={(e) => handleFormChange('year', e.target.value)}
                label="Năm học"
              >
                {generateYearOptions().map((year) => (
                  <MenuItem key={year} value={year.toString()}>
                    {year}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth>
              <InputLabel>Học kỳ</InputLabel>
              <Select
                value={formData.semester}
                onChange={(e) => handleFormChange('semester', e.target.value)}
                label="Học kỳ"
              >
                <MenuItem value="1">Học kỳ 1 (Fall)</MenuItem>
                <MenuItem value="2">Học kỳ 2 (Spring)</MenuItem>
                <MenuItem value="3">Học kỳ 3 (Summer)</MenuItem>
              </Select>
            </FormControl>

            <Grid container spacing={2}>
              <Grid size={6}>
                <DatePicker
                  label="Ngày bắt đầu"
                  value={formData.startDate}
                  onChange={(value) => handleFormChange('startDate', value)}
                  slotProps={{ textField: { fullWidth: true } }}
                />
              </Grid>
              <Grid size={6}>
                <DatePicker
                  label="Ngày kết thúc"
                  value={formData.endDate}
                  onChange={(value) => handleFormChange('endDate', value)}
                  slotProps={{ textField: { fullWidth: true } }}
                />
              </Grid>
            </Grid>
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Hủy</Button>
          <Button variant="contained" onClick={handleCreateSemester}>
            Tạo học kỳ
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={openConfirm}
        onClose={() => setOpenConfirm(false)}
        title="Xóa học kỳ"
        content={`Bạn có chắc chắn muốn xóa học kỳ ${selectedSemester?.year} - ${getSemesterLabel(selectedSemester?.semester || 0)}?`}
        action={
          <Button variant="contained" color="error" onClick={handleDeleteSemester}>
            Xóa
          </Button>
        }
      />
    </DashboardContent>
  );
}
