'use client';

import { useCallback } from 'react';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';

import { CONFIG } from 'src/global-config';

import { Iconify } from 'src/components/iconify';

import { signInWithPassword } from '../context/keycloak/action';
import { KeycloakDebug } from './keycloak-debug';

// ----------------------------------------------------------------------

export function KeycloakLogin() {
  const handleSignIn = useCallback(async () => {
    try {
      await signInWithPassword();
    } catch (error) {
      console.error('Keycloak sign in error:', error);
    }
  }, []);

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: 3,
          p: 3,
        }}
      >
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="h4" sx={{ mb: 1 }}>
            Đăng nhập
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Sử dụng tài khoản Keycloak của bạn để đăng nhập
          </Typography>
        </Box>

        <Button
          fullWidth
          size="large"
          variant="contained"
          onClick={handleSignIn}
          startIcon={<Iconify icon="mdi:login" />}
          sx={{
            maxWidth: 400,
            py: 1.5,
          }}
        >
          Đăng nhập với Keycloak
        </Button>

        <Typography variant="caption" color="text.secondary" sx={{ textAlign: 'center', maxWidth: 400 }}>
          Bạn sẽ được chuyển hướng đến trang đăng nhập Keycloak để xác thực.
          Sau khi đăng nhập thành công, bạn sẽ được chuyển về ứng dụng.
        </Typography>
      </Box>

      <KeycloakDebug />
    </>
  );
}
