import type { StudentWithGrades } from 'src/types/academic';

import { getEnroll } from './ttu-api';

export type EnrollResponse = {
  rows: StudentWithGrades[];
  total: number;
};

export async function fetchEnrollments(
  classId: string,
  page: number,
  perPage: number
): Promise<EnrollResponse> {
  const res = await getEnroll(page, perPage, classId);
  const payload = (res as any)?.data;

  // API returns: { code: 200, message: "Success", data: { total: 16, data: [...] } }
  const dataWrapper = payload?.data ?? payload;
  const list = dataWrapper?.data ?? dataWrapper?.rows ?? dataWrapper?.items ?? [];
  const total = Number(
    dataWrapper?.total ?? payload?.total ?? payload?.pagination?.total ?? list.length
  );

  return {
    rows: Array.isArray(list) ? list.map(mapTtuEnrollToStudent) : [],
    total,
  };
}
export async function fetchEnrollmentTotal(classId: string): Promise<number> {
  const res = await getEnroll(1, 1, classId);
  const payload = (res as any)?.data;
  // API returns: { code: 200, message: "Success", data: { total: 16, data: [...] } }
  const dataWrapper = payload?.data ?? payload;
  const total = Number(dataWrapper?.total ?? payload?.total ?? 0);
  return total;
}

export async function fetchAllClassStudents(classId: string): Promise<StudentWithGrades[]> {
  // Get all students without pagination (perPage=-1)
  const res = await getEnroll(1, -1, classId);
  const payload = (res as any)?.data;

  // API returns: { code: 200, message: "Success", data: { total: 16, data: [...] } }
  // So we need to access payload.data.data for the actual array
  const list = payload?.data?.data ?? payload?.data ?? payload?.rows ?? payload?.items ?? [];

  // Ensure list is an array before mapping
  return Array.isArray(list) ? list.map(mapTtuEnrollToStudent) : [];
}

export function mapTtuEnrollToStudent(raw: any): StudentWithGrades {
  // TTU API structure: enrollment record contains student object
  const student = raw.student ?? raw;
  // Use student._id as the main ID, not the enrollment record _id
  const _id = `${student._id ?? raw._id ?? raw.id ?? Math.random()}`;

  // Extract student info from nested student object
  const studentId = String(student.student_id ?? student.code ?? student._id ?? _id);

  const fullName = String(
    student.fullName ??
      student.full_name ??
      (`${student.last_name ?? ''} ${student.first_name ?? ''}`.trim() || 'Sinh viên')
  );

  const email = student.email ?? undefined;
  const status = (raw.status ?? student.status ?? 'active') as any;
  const avatar = student.avatar
    ? `https://intranet.ttu.edu.vn/uploads/${student.avatar}`
    : undefined;

  return {
    _id,
    studentId,
    fullName,
    email,
    status,
    avatar,
    // Optional demo fields until API provides them
    attendanceRate: raw.attendanceRate ?? undefined,
    totalScore: raw.totalScore ?? undefined,
  };
}
