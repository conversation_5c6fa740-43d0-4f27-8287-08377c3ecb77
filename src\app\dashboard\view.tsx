'use client';

import Typography from '@mui/material/Typography';

import { DashboardContent } from 'src/layouts/dashboard';

import { useUserProfile } from 'src/auth/hooks';

import { ShortcutsWidget } from './shortcuts-widget';

// ----------------------------------------------------------------------

export function TeacherDashboardView() {
  const { user } = useUserProfile();

  return (
    <DashboardContent>
      <Typography variant="h4" sx={{ mb: 1 }}>
        Chào mừng trở lại, {user?.displayName || 'Giảng viên'}! 👋
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: { xs: 3, md: 5 } }}>
        Chọn một lối tắt để bắt đầu quản lý công việc của bạn.
      </Typography>

      <ShortcutsWidget />
    </DashboardContent>
  );
}
