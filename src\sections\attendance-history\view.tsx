'use client';

import { useState, useEffect, useCallback } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Chip from '@mui/material/Chip';
import Table from '@mui/material/Table';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import Tooltip from '@mui/material/Tooltip';
import TableRow from '@mui/material/TableRow';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import DialogContentText from '@mui/material/DialogContentText';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { DashboardContent } from 'src/layouts/dashboard';
import {
  formatDate,
  getTimeAgo,
  getSemesterLabel,
  type HistoryFilters,
  fetchAttendanceHistory,
  deleteAttendanceHistory,
  type AttendanceHistoryItem,
} from 'src/actions/ttu-history-adapter';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';
import { TablePaginationCustom } from 'src/components/table';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';

import { HistoryFilters as HistoryFiltersComponent } from './history-filters';

export default function AttendanceHistoryView() {
  const [items, setItems] = useState<AttendanceHistoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [total, setTotal] = useState(0);
  const [filters, setFilters] = useState<HistoryFilters>({
    page: 1,
    perPage: 10,
  });

  const [deleteDialog, setDeleteDialog] = useState<{
    open: boolean;
    item: AttendanceHistoryItem | null;
  }>({ open: false, item: null });

  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetchAttendanceHistory(filters);
      setItems(response.items);
      console.log('response', response);

      setTotal(response.total);
    } catch (error) {
      console.error('Failed to load attendance history:', error);
      toast.error('Có lỗi xảy ra khi tải lịch sử điểm danh');
    } finally {
      setLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  const handleFilterChange = (newFilters: Partial<HistoryFilters>) => {
    setFilters((prev) => ({
      ...prev,
      ...newFilters,
      page: 1, // Reset to first page when filters change
    }));
  };

  const handlePageChange = (event: unknown, newPage: number) => {
    setFilters((prev) => ({
      ...prev,
      page: newPage + 1, // MUI pagination is 0-based, API is 1-based
    }));
  };

  const handleRowsPerPageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFilters((prev) => ({
      ...prev,
      perPage: parseInt(event.target.value, 10),
      page: 1,
    }));
  };

  const handleDeleteClick = (item: AttendanceHistoryItem) => {
    setDeleteDialog({ open: true, item });
  };

  const handleDeleteConfirm = async () => {
    if (!deleteDialog.item) return;

    try {
      await deleteAttendanceHistory(deleteDialog.item.id);
      toast.success('Xóa phiên điểm danh thành công');
      setDeleteDialog({ open: false, item: null });
      loadData(); // Reload data
    } catch (error) {
      console.error('Failed to delete attendance:', error);
      toast.error('Có lỗi xảy ra khi xóa phiên điểm danh');
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialog({ open: false, item: null });
  };

  const getStatusColor = (locked: boolean) => (locked ? 'success' : 'success');

  const getStatusLabel = (locked: boolean) => (locked ? 'Đã chốt' : 'Hoàn thành');

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="Lịch sử điểm danh"
        links={[{ name: 'Trang chủ', href: paths.dashboard.root }, { name: 'Lịch sử điểm danh' }]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card>
        <HistoryFiltersComponent
          filters={filters}
          onFiltersChange={handleFilterChange}
          loading={loading}
        />

        <Scrollbar>
          <Table size="small" sx={{ minWidth: 800 }}>
            <TableHead>
              <TableRow>
                <TableCell>Thông tin phiên điểm danh</TableCell>
                <TableCell>Trạng thái</TableCell>
                <TableCell>Thời gian tạo</TableCell>
                <TableCell align="right">Hành động</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={4} align="center">
                    <Typography variant="body2" color="text.secondary">
                      Đang tải...
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : items.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} align="center">
                    <Typography variant="body2" color="text.secondary">
                      Không có dữ liệu lịch sử điểm danh
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                items.map((item) => (
                  <TableRow key={item.id} hover>
                    <TableCell>
                      <Box>
                        <Typography variant="subtitle2" noWrap>
                          {item.className} - {item.classCode}
                        </Typography>
                        <Typography variant="body2" color="text.secondary" noWrap>
                          {item.content} - {formatDate(item.date)}
                        </Typography>
                        <Stack direction="row" spacing={1} sx={{ mt: 0.5 }}>
                          <Chip
                            label={getSemesterLabel(item.semester)}
                            size="small"
                            variant="outlined"
                          />
                          <Chip label={`Năm ${item.year}`} size="small" variant="outlined" />
                        </Stack>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getStatusLabel(item.locked)}
                        color={getStatusColor(item.locked) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2">{item.createdBy}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {getTimeAgo(item.createdAt)}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="right">
                      <Stack direction="row" spacing={1} justifyContent="flex-end">
                        <Tooltip title="Xem chi tiết">
                          <IconButton
                            component={RouterLink}
                            href={paths.dashboard.classes.attendanceSession(item.classId, item.id)}
                            color="primary"
                            size="small"
                          >
                            <Iconify icon="solar:eye-bold" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Xóa phiên điểm danh">
                          <IconButton
                            onClick={() => handleDeleteClick(item)}
                            color="error"
                            size="small"
                          >
                            <Iconify icon="solar:trash-bin-trash-bold" />
                          </IconButton>
                        </Tooltip>
                      </Stack>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </Scrollbar>

        <TablePaginationCustom
          count={total}
          page={filters.page ? filters.page - 1 : 0} // Convert back to 0-based for MUI
          rowsPerPage={filters.perPage || 10}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleRowsPerPageChange}
          dense
        />
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialog.open} onClose={handleDeleteCancel} maxWidth="sm" fullWidth>
        <DialogTitle>Xác nhận xóa phiên điểm danh</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Bạn có chắc chắn muốn xóa phiên điểm danh{' '}
            <strong>
              {deleteDialog.item?.content} - {deleteDialog.item?.className}
            </strong>
            ?
            <br />
            <br />
            <strong>Lưu ý:</strong> Hành động này không thể hoàn tác và sẽ xóa tất cả dữ liệu điểm
            danh của phiên này.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} color="inherit">
            Hủy
          </Button>
          <Button onClick={handleDeleteConfirm} color="error" variant="contained">
            Xóa
          </Button>
        </DialogActions>
      </Dialog>
    </DashboardContent>
  );
}
