'use client';

import { useState } from 'react';

import {
  Box,
  <PERSON>,
  Alert,
  Button,
  TextField,
  Accordion,
  Typography,
  CircularProgress,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';

import { CONFIG } from 'src/global-config';

// ----------------------------------------------------------------------

export default function KeycloakClientCheckPage() {
  const [loading, setLoading] = useState(false);
  const [clientConfig, setClientConfig] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [adminToken, setAdminToken] = useState('');

  const checkClientConfig = async () => {
    if (!adminToken.trim()) {
      setError('Please provide admin access token');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Get client configuration from Keycloak
      const response = await fetch(
        `${CONFIG.keycloak.authority}/admin/realms/ttu/clients?clientId=${CONFIG.keycloak.clientId}`,
        {
          headers: {
            'Authorization': `Bearer ${adminToken}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const clients = await response.json();
      if (clients.length === 0) {
        throw new Error(`Client '${CONFIG.keycloak.clientId}' not found`);
      }

      setClientConfig(clients[0]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const getAdminToken = async () => {
    setLoading(true);
    setError(null);

    try {
      // This is just for demonstration - in real scenario, admin should provide token
      setError('Please get admin token manually from Keycloak Admin Console → Realm Settings → Keys → RS256 → Certificate');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const expectedRedirectUris = [
    CONFIG.keycloak.redirectUri,
    CONFIG.keycloak.postLogoutRedirectUri,
    `${window.location.origin}/*`
  ];

  const expectedPostLogoutUris = [
    CONFIG.keycloak.postLogoutRedirectUri,
    `${window.location.origin}/*`
  ];

  const expectedWebOrigins = [
    window.location.origin
  ];

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        Keycloak Client Configuration Checker
      </Typography>

      {/* Instructions */}
      <Alert severity="info" sx={{ mb: 3 }}>
        <Typography variant="body2">
          This tool helps verify your Keycloak client configuration. You need an admin access token to check the actual client settings.
        </Typography>
      </Alert>

      {/* Expected Configuration */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Expected Configuration
        </Typography>

        <Accordion>
          <AccordionSummary>
            <Typography>▼ Valid Redirect URIs</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
              {expectedRedirectUris.map((uri, index) => (
                <div key={index}>{uri}</div>
              ))}
            </Box>
          </AccordionDetails>
        </Accordion>

        <Accordion>
          <AccordionSummary>
            <Typography>▼ Valid Post Logout Redirect URIs (CRITICAL)</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
              {expectedPostLogoutUris.map((uri, index) => (
                <div key={index}>{uri}</div>
              ))}
            </Box>
          </AccordionDetails>
        </Accordion>

        <Accordion>
          <AccordionSummary>
            <Typography>▼ Web Origins</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
              {expectedWebOrigins.map((origin, index) => (
                <div key={index}>{origin}</div>
              ))}
            </Box>
          </AccordionDetails>
        </Accordion>
      </Paper>

      {/* Admin Token Input */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Check Actual Client Configuration
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          To check the actual client configuration, you need an admin access token.
        </Typography>

        <TextField
          fullWidth
          label="Admin Access Token"
          type="password"
          value={adminToken}
          onChange={(e) => setAdminToken(e.target.value)}
          placeholder="Paste admin access token here"
          sx={{ mb: 2 }}
        />

        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="contained"
            onClick={checkClientConfig}
            disabled={loading || !adminToken.trim()}
          >
            {loading ? <CircularProgress size={20} /> : 'Check Client Config'}
          </Button>
          <Button
            variant="outlined"
            onClick={() => window.open('http://localhost:8080/admin', '_blank')}
          >
            Open Keycloak Admin
          </Button>
        </Box>
      </Paper>

      {/* Error Display */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Client Configuration Results */}
      {clientConfig && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Actual Client Configuration
          </Typography>

          <Accordion>
            <AccordionSummary>
              <Typography>▼ Redirect URIs</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Box sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
                {clientConfig.redirectUris?.map((uri: string, index: number) => (
                  <div key={index}>{uri}</div>
                )) || 'None configured'}
              </Box>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary>
              <Typography>▼ Post Logout Redirect URIs</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Box sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
                {clientConfig.attributes?.['post.logout.redirect.uris']?.split('##')?.map((uri: string, index: number) => (
                  <div key={index}>{uri}</div>
                )) || 'None configured'}
              </Box>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary>
              <Typography>▼ Web Origins</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Box sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
                {clientConfig.webOrigins?.map((origin: string, index: number) => (
                  <div key={index}>{origin}</div>
                )) || 'None configured'}
              </Box>
            </AccordionDetails>
          </Accordion>

          <Accordion>
            <AccordionSummary>
              <Typography>▼ Other Settings</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Box sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
                <div>Public Client: {clientConfig.publicClient ? 'Yes' : 'No'}</div>
                <div>Standard Flow: {clientConfig.standardFlowEnabled ? 'Yes' : 'No'}</div>
                <div>Direct Access Grants: {clientConfig.directAccessGrantsEnabled ? 'Yes' : 'No'}</div>
              </Box>
            </AccordionDetails>
          </Accordion>
        </Paper>
      )}

      {/* Manual Configuration Guide */}
      <Paper sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Manual Configuration Steps
        </Typography>
        <Typography variant="body2" paragraph>
          If the automatic check doesn&apos;t work, follow these manual steps:
        </Typography>
        <Box component="ol" sx={{ pl: 2 }}>
          <li>
            <Typography variant="body2">
              Go to <strong>Keycloak Admin Console</strong> → <strong>Clients</strong> → <strong>test01</strong>
            </Typography>
          </li>
          <li>
            <Typography variant="body2">
              In <strong>Settings</strong> tab, scroll to <strong>Access settings</strong>
            </Typography>
          </li>
          <li>
            <Typography variant="body2">
              Add to <strong>Valid post logout redirect URIs</strong>:
              <br />
              <code>{CONFIG.keycloak.postLogoutRedirectUri}</code>
              <br />
              <code>{window.location.origin}{'/*'}</code>
            </Typography>
          </li>
          <li>
            <Typography variant="body2">
              Click <strong>Save</strong>
            </Typography>
          </li>
          <li>
            <Typography variant="body2">
              Test logout again
            </Typography>
          </li>
        </Box>
      </Paper>
    </Box>
  );
}
