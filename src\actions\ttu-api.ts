import axios from 'src/lib/axios';

// Helper to build query params object easily
const toParams = (obj: Record<string, any>) => ({ params: obj });

// Classes
export const getClass = async (year: string | number) =>
  axios.get(`student/class`, toParams({ year, perPage: -1 }));

export const getDataClass = async (year: string | number, semester: string | number) =>
  axios.get(`student/class`, toParams({ year, semester, perPage: -1 }));

// Enrollments
export const getEnroll = async (
  page: number,
  perPage: number,
  classId: string | number,
  search?: string
) => {
  const params: any = { page, perPage, _class: classId };
  if (search) {
    params.q = search;
  }
  return axios.get(`student/enroll`, toParams(params));
};

// Attendance
export const createAttendance = async (data: any) => axios.post(`student/attendance`, data);

export const updateAttendance = async (id: string | number, data: any) =>
  axios.patch(`student/attendance/${id}`, data);

export const getAttendance = async (page: number, perPage: number) =>
  axios.get(`student/attendance`, toParams({ page, perPage }));

export const deleteAttendance = async (id: string | number) =>
  axios.delete(`student/attendance/${id}`);

export const getAttendanceDetails = async (id: string | number) =>
  axios.get(`student/absent`, toParams({ attendance: id }));

// Get attendance session info
export const getAttendanceSession = async (id: string | number) =>
  axios.get(`student/attendance/${id}`);

// Absence
export const getAbsentClass = async (
  year: string | number,
  semester: string | number,
  _class: string | number
) => axios.get(`student/absent/list-absence`, toParams({ year, semester, _class }));

export const getAbsentStudent = async (
  year: string | number,
  semester: string | number,
  student: string | number
) => axios.get(`student/absent/student-absence`, toParams({ year, semester, student }));

// Create absent record
export const createAbsent = async (data: {
  class: string | number;
  student: string | number;
  attendance: string | number;
  is_permission?: number; // 1: có phép, 2: không phép
}) => axios.post(`student/absent`, data);

// Delete absent record
export const deleteAbsent = async (id: string | number) => axios.delete(`student/absent/${id}`);

// Edit absent record
export const editAbsent = async (id: string | number, data: any) =>
  axios.patch(`student/absent/${id}`, data);

export const getAbsentStudentDetails = async (student: string | number, _class: string | number) =>
  axios.get(`student/absent/list-absence`, toParams({ student, _class }));

export const getAbsentSemester = async (year: string | number, semester: string | number) =>
  axios.get(`student/absent/list-absence`, toParams({ year, semester }));

export const getAbsentYear = async (year: string | number, perPage: number) =>
  axios.get(`student/absent/list-absence`, toParams({ year, perPage }));

// Attendance permission
export const getPermission = async (page: number, perPage: number, classId: string | number) =>
  axios.get(`student/attendance-permission`, toParams({ page, perPage, _class: classId }));

// Reports
export const getReport = async (classId: string | number) =>
  axios.get(`student/attendance/report-preview`, toParams({ _class: classId }));

export const downloadReport = async (year: string | number, classId: string | number) =>
  axios.get(`student/attendance/report`, toParams({ year, _class: classId }));

// Grade Components
export const getGradeComponents = async (params: {
  year?: number;
  semester?: number;
  _class?: number;
  page?: number;
  perPage?: number;
}) => axios.get(`student/grade-components`, toParams(params));

export const createGradeComponent = async (data: any) =>
  axios.post(`student/grade-components`, data);

export const updateGradeComponent = async (id: string | number, data: any) =>
  axios.patch(`student/grade-components/${id}`, data);

// Component Scores
export const createComponentScore = async (data: any) =>
  axios.post(`student/component-scores`, data);

export const getComponentScores = async (params: {
  page?: number;
  perPage?: number;
  grade_component?: number;
  class?: number;
  student?: number;
}) => axios.get(`student/component-scores`, toParams(params));

export const filterAttendance = async (params: Record<string, any>) =>
  axios.get(`student/attendance`, { params });

// Attendance history
export const getAttendanceHistory = async (
  page: number,
  perPage: number,
  params?: Record<string, any>
) => {
  const queryParams = { page, perPage, ...params };
  return axios.get(`student/attendance`, toParams(queryParams));
};

// Student absent reports - using existing endpoints with different names
export const getAbsentSemesterReport = async (year: string, semester: string) =>
  axios.get(`student/absent/list-absence`, toParams({ year, semester }));

export const getAbsentClassReport = async (year: string, semester: string, classId: string) =>
  axios.get(`student/absent/list-absence`, toParams({ year, semester, _class: classId }));

export const getAbsentStudentReport = async (studentId: string, classId: string) =>
  axios.get(`student/absent/list-absence`, toParams({ student: studentId, _class: classId }));

// Get student absent details by year/semester (all classes)
export const getAbsentStudentByYearSemester = async (
  year: string,
  semester: string,
  studentId: string
) => axios.get(`student/absent/list-absence`, toParams({ year, semester, student: studentId }));

// Get student details
export const getStudentDetails = async (studentId: string) =>
  axios.get(`student/student/${studentId}`);

export const postPermission = async (data: any) =>
  axios.post(`student/attendance-permission`, data);

export const deletePermission = async (id: string | number) =>
  axios.delete(`student/attendance-permission/${id}`);

// Calendar
export const getCalendar = async (page: number, perPage: number) =>
  axios.get(`student/calendar`, toParams({ page, perPage }));

export const getCalendarNow = async () => axios.get(`student/calendar/now`, toParams({}));

export const postCalendar = async (data: any) => axios.post(`student/calendar`, data);

export const deleteCalendar = async (id: string | number) => axios.delete(`student/calendar/${id}`);

// Auth
export const login = async (data: any) => axios.post(`auth/login`, data);
