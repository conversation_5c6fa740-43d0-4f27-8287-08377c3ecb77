'use client';

import { useEffect, useState, useCallback } from 'react';

import Card from '@mui/material/Card';
import Grid from '@mui/material/Grid2';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';

import { paths } from 'src/routes/paths';
import { DashboardContent } from 'src/layouts/dashboard';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';
import { Iconify } from 'src/components/iconify';

import { ReportFilters, type ReportFiltersState } from './report-filters';
import AttendanceReportTable from './attendance-report-table';
import {
  fetchClassOptions,
  fetchAttendanceReport,
  downloadAttendanceReport,
  type ReportData,
} from 'src/actions/ttu-report-adapter';
import { toast } from 'src/components/snackbar';

export default function ReportsView() {
  const [filters, setFilters] = useState<ReportFiltersState>({});
  const [classOptions, setClassOptions] = useState<Array<{ value: string; label: string }>>([]);
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [loading, setLoading] = useState(false);

  const handleChangeFilters = useCallback((name: keyof ReportFiltersState, value: string) => {
    setFilters((prev) => ({ ...prev, [name]: value }));
  }, []);

  useEffect(() => {
    const loadClasses = async () => {
      if (filters.year) {
        const opts = await fetchClassOptions(filters.year, filters.semester);
        setClassOptions(opts);
      } else {
        setClassOptions([]);
      }
    };
    loadClasses();
  }, [filters.year, filters.semester]);

  const handlePreview = useCallback(async () => {
    if (!filters.classId) {
      toast.warning('Vui lòng chọn lớp học');
      return;
    }

    try {
      setLoading(true);
      const data = await fetchAttendanceReport(filters.classId);
      setReportData(data);
      toast.success('Tạo báo cáo thành công');
    } catch (error) {
      console.error('Error generating report:', error);
      toast.error('Có lỗi xảy ra khi tạo báo cáo');
    } finally {
      setLoading(false);
    }
  }, [filters.classId]);

  const handleDownload = useCallback(async () => {
    if (!filters.year || !filters.classId) {
      toast.warning('Vui lòng chọn năm học và lớp học');
      return;
    }

    try {
      setLoading(true);
      const downloadUrl = await downloadAttendanceReport(filters.year, filters.classId);

      // Use window.location.href like the original project
      window.location.href = downloadUrl;

      toast.success('Đang tải file báo cáo...');
    } catch (error) {
      console.error('Error downloading report:', error);
      toast.error('Có lỗi xảy ra khi tải báo cáo: ' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  }, [filters.year, filters.classId]);

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="Báo cáo"
        links={[{ name: 'Trang chủ', href: paths.dashboard.root }, { name: 'Báo cáo' }]}
        action={
          <Stack direction="row" spacing={1}>
            <Button
              variant="outlined"
              onClick={handlePreview}
              startIcon={<Iconify icon="solar:eye-bold" />}
              disabled={loading || !filters.classId}
            >
              {loading ? 'Đang tạo...' : 'Tạo báo cáo'}
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={handleDownload}
              startIcon={<Iconify icon="solar:export-bold" />}
              disabled={loading || !filters.year || !filters.classId}
            >
              {loading ? 'Đang tải...' : 'Tải Excel'}
            </Button>
          </Stack>
        }
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card sx={{ mb: 3 }}>
        <ReportFilters
          filters={filters}
          onChange={handleChangeFilters}
          classOptions={classOptions}
        />
      </Card>

      <Grid container spacing={3}>
        <Grid size={{ xs: 12 }}>
          {reportData ? (
            <AttendanceReportTable reportData={reportData} />
          ) : (
            <Card sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Báo cáo điểm danh
              </Typography>
              <Typography variant="body2" color="text.secondary" align="center">
                Chọn năm học, học kỳ, lớp học và nhấn "Tạo báo cáo" để hiển thị báo cáo chi tiết
              </Typography>
            </Card>
          )}
        </Grid>
      </Grid>
    </DashboardContent>
  );
}
