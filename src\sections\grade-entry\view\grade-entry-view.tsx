'use client';

import type { GradeComponent } from 'src/types/academic';

import { useState, useEffect } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Chip from '@mui/material/Chip';
import Grid from '@mui/material/Grid2';
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import CardContent from '@mui/material/CardContent';
import InputAdornment from '@mui/material/InputAdornment';
import CardActionArea from '@mui/material/CardActionArea';
import CircularProgress from '@mui/material/CircularProgress';

import { paths } from 'src/routes/paths';
import { useSearchParams } from 'src/routes/hooks';

import { DashboardContent } from 'src/layouts/dashboard';
import { getClass, getGradeComponents } from 'src/actions/ttu-api';

import { Iconify } from 'src/components/iconify';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';

import { GradeEntryTable } from '../grade-entry-table';

// ----------------------------------------------------------------------

type ClassInfo = {
  _id: number;
  year: number;
  lecturer: number;
  name: string;
  name_vn: string;
  code: string;
  school: number;
  semester: number;
};

type EnhancedGradeComponent = GradeComponent & {
  class_info?: ClassInfo;
};

export function GradeEntryView() {
  const searchParams = useSearchParams();

  const [gradeComponents, setGradeComponents] = useState<EnhancedGradeComponent[]>([]);
  const [filteredComponents, setFilteredComponents] = useState<EnhancedGradeComponent[]>([]);
  const [selectedComponent, setSelectedComponent] = useState<EnhancedGradeComponent | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);

  // Load all grade components and class info
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);

        // Load grade components
        const componentsResponse = await getGradeComponents({
          page: 1,
          perPage: -1,
        });
        const components = componentsResponse?.data?.data?.data || [];

        // Load all classes to get class info
        const classResponse = await getClass(2024); // You might want to make this dynamic
        const classes = classResponse?.data?.data?.data || [];

        // Create class map for quick lookup
        const classMap = new Map<number, ClassInfo>();
        classes.forEach((cls: ClassInfo) => {
          classMap.set(cls._id, cls);
        });

        // Enhance components with class info
        const enhancedComponents: EnhancedGradeComponent[] = components.map(
          (component: GradeComponent) => ({
            ...component,
            class_info: classMap.get(component.class),
          })
        );

        setGradeComponents(enhancedComponents);
        setFilteredComponents(enhancedComponents);
      } catch (error) {
        console.error('Failed to load data:', error);
      } finally {
        setLoading(false);
      }
    };
    loadData();
  }, []);

  // Filter components based on search term
  useEffect(() => {
    if (!searchTerm) {
      setFilteredComponents(gradeComponents);
    } else {
      const filtered = gradeComponents.filter(
        (component) =>
          component.name_vie.toLowerCase().includes(searchTerm.toLowerCase()) ||
          component.name_eng.toLowerCase().includes(searchTerm.toLowerCase()) ||
          component.class_info?.name_vn?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          component.class_info?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          component.class_info?.code?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredComponents(filtered);
    }
  }, [searchTerm, gradeComponents]);

  // Auto-select component from URL parameter
  useEffect(() => {
    const componentId = searchParams.get('componentId');
    if (componentId && gradeComponents.length > 0) {
      const component = gradeComponents.find((comp) => String(comp._id) === componentId);
      if (component) {
        setSelectedComponent(component);
      }
    }
  }, [searchParams, gradeComponents]);

  const handleComponentSelect = (component: EnhancedGradeComponent) => {
    setSelectedComponent(component);
  };

  const handleBackToList = () => {
    setSelectedComponent(null);
  };

  const getSemesterLabel = (semester: number) => {
    switch (semester) {
      case 1:
        return 'HK1';
      case 2:
        return 'HK2';
      case 3:
        return 'HK3';
      default:
        return `HK${semester}`;
    }
  };

  // Show grade entry table if component is selected
  if (selectedComponent) {
    return (
      <DashboardContent>
        <CustomBreadcrumbs
          heading="Nhập điểm"
          links={[
            { name: 'Trang chủ', href: paths.dashboard.root },
            { name: 'Nhập điểm', href: paths.dashboard.gradeEntry },
            { name: selectedComponent.name_vie },
          ]}
          action={
            <Button
              variant="outlined"
              startIcon={<Iconify icon="eva:arrow-back-fill" />}
              onClick={handleBackToList}
            >
              Quay lại danh sách
            </Button>
          }
          sx={{ mb: { xs: 3, md: 5 } }}
        />

        <GradeEntryTable
          classId={selectedComponent.class}
          gradeComponentId={Number(selectedComponent._id)}
          year={selectedComponent.year}
          semester={selectedComponent.semester}
          gradeComponent={selectedComponent}
        />
      </DashboardContent>
    );
  }

  // Show component list
  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="Nhập điểm"
        links={[{ name: 'Trang chủ', href: paths.dashboard.root }, { name: 'Nhập điểm' }]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Grid container spacing={3}>
        <Grid size={{ xs: 12 }}>
          <Card sx={{ p: 3 }}>
            <Box
              sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}
            >
              <Typography variant="h6">Danh sách thành phần điểm</Typography>
              <Typography variant="body2" color="text.secondary">
                {loading ? 'Đang tải...' : `${filteredComponents.length} thành phần`}
              </Typography>
            </Box>

            <TextField
              fullWidth
              placeholder="Tìm kiếm theo tên thành phần, lớp học, mã lớp..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              slotProps={{
                input: {
                  startAdornment: (
                    <InputAdornment position="start">
                      <Iconify icon="eva:search-fill" />
                    </InputAdornment>
                  ),
                },
              }}
              sx={{ mb: 3 }}
            />

            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : (
              <Grid container spacing={2}>
                {filteredComponents.map((component) => (
                  <Grid key={component._id} size={{ xs: 12, md: 6, lg: 4 }}>
                    <Card
                      sx={{
                        height: '100%',
                        transition: 'all 0.2s',
                        '&:hover': {
                          transform: 'translateY(-2px)',
                          boxShadow: 4,
                        },
                      }}
                    >
                      <CardActionArea
                        onClick={() => handleComponentSelect(component)}
                        sx={{ height: '100%' }}
                      >
                        <CardContent sx={{ p: 2.5 }}>
                          <Typography
                            variant="h6"
                            sx={{ fontWeight: 600, mb: 1, fontSize: '1rem' }}
                          >
                            {component.name_vie}
                          </Typography>

                          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                            {component.name_eng}
                          </Typography>

                          <Box sx={{ mb: 2 }}>
                            <Typography variant="body2" sx={{ fontWeight: 500, mb: 0.5 }}>
                              📚{' '}
                              {component.class_info?.name_vn ||
                                component.class_info?.name ||
                                'Không có thông tin lớp'}
                            </Typography>
                          </Box>

                          <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                            <Chip
                              label={component.class_info?.code || 'N/A'}
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                            <Chip
                              label={`${getSemesterLabel(component.semester)} ${component.year}`}
                              size="small"
                              color="secondary"
                              variant="outlined"
                            />
                            <Chip
                              label={`${component.weight}%`}
                              size="small"
                              color="success"
                              variant="outlined"
                            />
                          </Box>
                        </CardContent>
                      </CardActionArea>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}

            {!loading && filteredComponents.length === 0 && (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography variant="body1" color="text.secondary">
                  {searchTerm
                    ? 'Không tìm thấy thành phần điểm nào'
                    : 'Chưa có thành phần điểm nào'}
                </Typography>
              </Box>
            )}
          </Card>
        </Grid>
      </Grid>
    </DashboardContent>
  );
}
