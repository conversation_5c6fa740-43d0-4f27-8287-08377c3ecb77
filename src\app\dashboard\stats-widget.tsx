'use client';

import { useState, useEffect } from 'react';

import Grid from '@mui/material/Grid2';
import Card from '@mui/material/Card';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Skeleton from '@mui/material/Skeleton';

import { Iconify } from 'src/components/iconify';
import { getDashboardOverview } from 'src/actions/ttu-report-adapter';

type StatData = {
  totalClasses: number;
  totalStudents: number;
  attendanceRate: number;
  pendingGrades: number;
};

export function StatsWidget() {
  const [stats, setStats] = useState<StatData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadStats = async () => {
      try {
        setLoading(true);
        const data = await getDashboardOverview();
        setStats(data);
      } catch (error) {
        console.error('Failed to load dashboard stats:', error);
      } finally {
        setLoading(false);
      }
    };
    loadStats();
  }, []);

  const STATS_CONFIG = [
    { title: 'Tổng số lớp', value: stats?.totalClasses, icon: 'solar:book-bold', color: 'primary' },
    {
      title: 'Tổng số sinh viên',
      value: stats?.totalStudents,
      icon: 'solar:users-group-rounded-bold',
      color: 'success',
    },
    {
      title: 'Tỷ lệ điểm danh',
      value: `${stats?.attendanceRate}%`,
      icon: 'solar:calendar-mark-bold',
      color: 'info',
    },
    {
      title: 'Cần nhập điểm',
      value: stats?.pendingGrades,
      icon: 'solar:pen-new-square-bold',
      color: 'warning',
    },
  ];

  return (
    <Grid container spacing={3}>
      {STATS_CONFIG.map((stat) => (
        <Grid key={stat.title} size={{ xs: 12, sm: 6, md: 3 }}>
          <Card sx={{ p: 3, display: 'flex', alignItems: 'center' }}>
            <Box sx={{ flexGrow: 1 }}>
              <Typography variant="subtitle2">{stat.title}</Typography>
              <Typography variant="h4" sx={{ mt: 1 }}>
                {loading ? <Skeleton width={80} /> : stat.value}
              </Typography>
            </Box>
            <Iconify icon={stat.icon} width={48} sx={{ color: `${stat.color}.main` }} />
          </Card>
        </Grid>
      ))}
    </Grid>
  );
}
