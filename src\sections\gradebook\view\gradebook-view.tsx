'use client';

import { useState, useCallback, useEffect } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Button from '@mui/material/Button';
import Container from '@mui/material/Container';
import CardHeader from '@mui/material/CardHeader';

import { paths } from 'src/routes/paths';
import { useSearchParams } from 'src/routes/hooks';

import { DashboardContent } from 'src/layouts/dashboard';

import { Iconify } from 'src/components/iconify';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';

import { GradebookTable } from '../gradebook-table';
import { GradeComponentDialog } from '../grade-component-dialog';

// ----------------------------------------------------------------------

export function GradebookView() {
  const searchParams = useSearchParams();

  const [openDialog, setOpenDialog] = useState(false);
  const [selectedClass, setSelectedClass] = useState<number | null>(null);
  const [selectedClassData, setSelectedClassData] = useState<any>(null);
  const [refreshKey, setRefreshKey] = useState(0);
  const [editComponent, setEditComponent] = useState<any>(null);

  // Auto-select class from URL parameters
  useEffect(() => {
    const classId = searchParams.get('classId');
    const year = searchParams.get('year');
    const semester = searchParams.get('semester');

    if (classId && year && semester) {
      const classIdNum = parseInt(classId, 10);
      const yearNum = parseInt(year, 10);
      const semesterNum = parseInt(semester, 10);

      // Set selected class
      setSelectedClass(classIdNum);

      // Create class data object
      const classData = {
        _id: classIdNum,
        year: yearNum,
        semester: semesterNum,
        course_id: 1271, // Default course ID
        class_code: classId,
        class_name: `Lớp ${classId}`,
      };

      setSelectedClassData(classData);
    }
  }, [searchParams]);

  const handleOpenDialog = useCallback(() => {
    setOpenDialog(true);
  }, []);

  const handleCloseDialog = useCallback(() => {
    setOpenDialog(false);
    setEditComponent(null);
  }, []);

  const handleEditComponent = useCallback((component: any) => {
    setEditComponent(component);
    setOpenDialog(true);
  }, []);

  const handleClassSelect = useCallback((classId: number, classData?: any) => {
    setSelectedClass(classId);
    setSelectedClassData(classData);
  }, []);

  const handleRefresh = useCallback(() => {
    setRefreshKey((prev) => prev + 1);
  }, []);

  return (
    <DashboardContent>
      <Container maxWidth="xl">
        <CustomBreadcrumbs
          heading="Sổ điểm lớp"
          links={[{ name: 'Dashboard', href: paths.dashboard.root }, { name: 'Sổ điểm lớp' }]}
          action={
            <Button
              variant="contained"
              startIcon={<Iconify icon="mingcute:add-line" />}
              onClick={handleOpenDialog}
              disabled={!selectedClass}
            >
              Thêm thành phần điểm
            </Button>
          }
          sx={{ mb: { xs: 3, md: 5 } }}
        />

        <Card>
          <CardHeader
            title="Quản lý thành phần điểm"
            subheader="Tạo và quản lý các thành phần điểm cho lớp học"
          />

          <Box sx={{ p: 3 }}>
            <GradebookTable
              onClassSelect={handleClassSelect}
              selectedClass={selectedClass}
              refreshKey={refreshKey}
              onEditComponent={handleEditComponent}
            />
          </Box>
        </Card>

        <GradeComponentDialog
          open={openDialog}
          onClose={handleCloseDialog}
          classId={selectedClass}
          classData={
            selectedClassData
              ? {
                  year: selectedClassData.year,
                  semester: selectedClassData.semester,
                  course: selectedClassData.course_id,
                }
              : undefined
          }
          onSuccess={handleRefresh}
          editComponent={editComponent}
        />
      </Container>
    </DashboardContent>
  );
}
