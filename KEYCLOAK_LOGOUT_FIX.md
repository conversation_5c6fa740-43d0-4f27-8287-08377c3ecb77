# Khắc phục lỗi Keycloak Logout bị kẹt

## 🚨 Vấn đề
Khi logout từ ứng dụng, người dùng bị redirect đến Keycloak logout URL nhưng gặp lỗi "Invalid redirect uri":

**URL logout:**
```
http://localhost:8080/realms/ttu/protocol/openid-connect/logout?id_token_hint=...&post_logout_redirect_uri=http%3A%2F%2Flocalhost%3A8082%2Fauth%2Fkeycloak%2Flogout-callback
```

**Lỗi hiển thị:**
- Trang Keycloak với thông báo "We are sorry..."
- "Invalid redirect uri"
- Nút "Back to Application"

## ✅ Giải pháp đã thực hiện

### 1. Sửa lỗi trong logout callback page
**File:** `src/app/auth/keycloak/logout-callback/page.tsx`

**Vấn đề:** <PERSON>u khi xử lý logout callback, trang redirect về chính nó thay vì về login page.

**Đã sửa:**
- Thay đổi `router.replace('/auth/keycloak/logout-callback')` thành `router.replace('/auth/login')`
- Thêm cleanup localStorage và sessionStorage
- Cải thiện UI với Material-UI components

### 2. Cấu hình Keycloak Client cần kiểm tra

Đăng nhập vào Keycloak Admin Console và kiểm tra client `test01`:

#### A. Valid Redirect URIs
Phải bao gồm **TẤT CẢ** các URL sau:
```
http://localhost:8082/auth/keycloak/callback
http://localhost:8082/auth/keycloak/logout-callback
http://localhost:8082/*
```

#### B. **QUAN TRỌNG: Valid Post Logout Redirect URIs**
Đây là nguyên nhân chính gây lỗi "Invalid redirect uri". Phải thêm:
```
http://localhost:8082/auth/keycloak/logout-callback
http://localhost:8082/*
```

**Lưu ý:** Trường này riêng biệt với "Valid Redirect URIs" và thường bị bỏ sót!

#### C. Web Origins
Phải bao gồm:
```
http://localhost:8082
```

#### D. Settings cần thiết
- **Access Type:** `public`
- **Standard Flow Enabled:** `ON`
- **Direct Access Grants Enabled:** `ON`

### 3. Kiểm tra Environment Variables
File `.env` hoặc `.env.local`:
```env
NEXT_PUBLIC_KEYCLOAK_AUTHORITY=http://localhost:8080/realms/ttu
NEXT_PUBLIC_KEYCLOAK_CLIENT_ID=test01
NEXT_PUBLIC_KEYCLOAK_REDIRECT_URI=http://localhost:8082/auth/keycloak/callback
NEXT_PUBLIC_KEYCLOAK_POST_LOGOUT_REDIRECT_URI=http://localhost:8082/auth/keycloak/logout-callback
NEXT_PUBLIC_KEYCLOAK_SCOPE=openid profile email
```

## 🛠️ Hướng dẫn cấu hình Keycloak Admin Console

### Bước 1: Đăng nhập Keycloak Admin
1. Truy cập: `http://localhost:8080/admin`
2. Đăng nhập với admin credentials
3. Chọn realm `ttu`

### Bước 2: Cấu hình Client
1. Vào **Clients** → Chọn client `test01`
2. Vào tab **Settings**
3. Cuộn xuống phần **Access settings**

### Bước 3: Cấu hình URLs (QUAN TRỌNG)
**Valid redirect URIs:**
```
http://localhost:8082/auth/keycloak/callback
http://localhost:8082/*
```

**Valid post logout redirect URIs:** (Trường này thường bị bỏ sót!)
```
http://localhost:8082/auth/keycloak/logout-callback
http://localhost:8082/*
```

**Web origins:**
```
http://localhost:8082
```

### Bước 4: Lưu cấu hình
1. Click **Save** ở cuối trang
2. Restart ứng dụng Next.js nếu cần

## 🔧 Cách kiểm tra và debug

### 1. Kiểm tra cấu hình
**Config Check Page:** `http://localhost:8082/auth/keycloak/config-check`
- Kiểm tra tất cả environment variables
- Validate URL format
- Hướng dẫn cấu hình Keycloak client
- Link trực tiếp đến Keycloak Admin

**Debug Page:** `http://localhost:8082/auth/keycloak/debug`
- Cấu hình Keycloak hiện tại
- Thông tin user đang đăng nhập
- Nút test logout
- Hướng dẫn troubleshooting

### 2. Kiểm tra Network Tab
Khi logout, kiểm tra trong Browser DevTools > Network:
1. Request đến `/auth/keycloak/logout-callback` phải trả về 200
2. Không có lỗi CORS
3. Redirect cuối cùng phải về `/auth/login`

### 3. Kiểm tra Console Logs
Mở Browser DevTools > Console để xem:
- "Keycloak logout callback successful"
- Không có error messages

## 🚀 Cách test

1. **Đăng nhập:** Truy cập `http://localhost:8082/auth/login`
2. **Đăng xuất:** Click nút logout trong ứng dụng
3. **Kiểm tra:** Phải được redirect về login page, không kẹt ở Keycloak

## 📋 Checklist khắc phục

- [ ] **Kiểm tra cấu hình:** Truy cập `/auth/keycloak/config-check` để validate
- [ ] **Sửa Keycloak Client:** Thêm Valid Post Logout Redirect URIs
- [ ] **Valid Redirect URIs:** Bao gồm callback và logout-callback URLs
- [ ] **Web Origins:** Thêm origin của ứng dụng
- [ ] **Access Type:** Đặt thành `public`
- [ ] **Environment variables:** Kiểm tra tất cả biến môi trường
- [ ] **Test logout:** Sử dụng debug page để test
- [ ] **Console logs:** Kiểm tra không có lỗi

## 🆘 Nếu vẫn không hoạt động

1. **Restart Keycloak server**
2. **Clear browser cache và localStorage**
3. **Kiểm tra Keycloak server logs**
4. **Thử với incognito/private browsing mode**
5. **Kiểm tra firewall/proxy settings**

## 📞 Debug URLs

- **Config check:** `http://localhost:8082/auth/keycloak/config-check`
- **Debug page:** `http://localhost:8082/auth/keycloak/debug`
- **Keycloak admin:** `http://localhost:8080/admin`
- **Keycloak realm:** `http://localhost:8080/realms/ttu`
