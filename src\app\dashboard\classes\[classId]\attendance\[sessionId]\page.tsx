import type { Metadata } from 'next';

import { CONFIG } from 'src/global-config';

import AttendanceSessionDetailsView from 'src/sections/classes/attendance/session-details';

// ----------------------------------------------------------------------

export const metadata: Metadata = { title: `Chi tiết phiên điểm danh | ${CONFIG.appName}` };

export default function Page({ params }: { params: { classId: string; sessionId: string } }) {
  return <AttendanceSessionDetailsView classId={params.classId} sessionId={params.sessionId} />;
}
