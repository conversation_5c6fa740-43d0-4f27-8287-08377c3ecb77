'use client';

import type { ClassFilter, ClassWithStats } from 'src/types/academic';

import dayjs from 'dayjs';
import { useState, useEffect, useCallback } from 'react';

import Card from '@mui/material/Card';
import Table from '@mui/material/Table';
import Button from '@mui/material/Button';
import TableBody from '@mui/material/TableBody';

import { paths } from 'src/routes/paths';
import { useRouter, useSearchParams } from 'src/routes/hooks';

import { DashboardContent } from 'src/layouts/dashboard';
import { fetchClassesByYear, fetchClassesByYearSemester } from 'src/actions/ttu-adapter';

import { Scrollbar } from 'src/components/scrollbar';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';
import {
  useTable,
  emptyRows,
  TableNoData,
  getComparator,
  TableEmptyRows,
  TableHeadCustom,
  TableSelectedAction,
  TablePaginationCustom,
} from 'src/components/table';

import ClassTableRow from './class-table-row';
import ClassTableToolbar from './class-table-toolbar';

const TABLE_HEAD = [
  { id: 'code', label: 'Mã lớp' },
  { id: 'name', label: 'Tên học phần' },
  { id: 'term', label: 'Năm - Học kỳ' },
  { id: 'actions', label: '', width: 88 },
];

export default function ClassListView() {
  const table = useTable();
  const router = useRouter();
  const searchParams = useSearchParams();

  const [tableData, setTableData] = useState<ClassWithStats[]>([]);
  const [filters, setFilters] = useState<ClassFilter>({
    year: String(dayjs().year()), // Set default year
  });

  useEffect(() => {
    const year = searchParams.get('year') || '';
    const semester = searchParams.get('semester') || '';
    const search = searchParams.get('search') || '';
    if (year || semester || search) {
      setFilters((prev) => ({ ...prev, year, semester, search }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const loadData = useCallback(async () => {
    try {
      let data;
      if (filters.year && filters.semester) {
        data = await fetchClassesByYearSemester(filters.year, filters.semester);
      } else if (filters.year) {
        data = await fetchClassesByYear(filters.year);
      } else {
        // Fallback if somehow year is not set
        const now = dayjs();
        data = await fetchClassesByYear(now.year());
      }
      setTableData(data);
    } catch (error) {
      console.error('Error loading classes:', error);
    }
  }, [filters]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  const dataFiltered = applyFilter({
    inputData: tableData,
    comparator: getComparator(table.order, table.orderBy),
    filters,
  });

  const dataInPage = dataFiltered.slice(
    table.page * table.rowsPerPage,
    table.page * table.rowsPerPage + table.rowsPerPage
  );

  const canReset = !!filters.search || !!filters.year || !!filters.semester || !!filters.status;

  const notFound = (!dataFiltered.length && canReset) || !dataFiltered.length;

  const handleFilters = useCallback(
    (name: string, value: string) => {
      table.onResetPage();
      setFilters((prevState) => ({
        ...prevState,
        [name]: value,
      }));
    },
    [table]
  );

  useEffect(() => {
    const params = new URLSearchParams();
    if (filters.year) params.set('year', String(filters.year));
    if (filters.semester) params.set('semester', String(filters.semester));
    if (filters.search) params.set('search', String(filters.search));
    const qs = params.toString();
    router.replace(`${paths.dashboard.classes.root}${qs ? `?${qs}` : ''}`);
  }, [filters, router]);

  const handleResetFilters = useCallback(() => {
    setFilters({});
  }, []);

  const handleDeleteRows = useCallback(() => {
    const deleteRows = tableData.filter((row) => !table.selected.includes(row.id));
    setTableData(deleteRows);
    table.onUpdatePageDeleteRows(dataInPage.length, dataFiltered.length);
  }, [dataFiltered.length, dataInPage.length, table, tableData]);

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="Danh sách lớp"
        links={[{ name: 'Trang chủ', href: paths.dashboard.root }, { name: 'Danh sách lớp' }]}
        // action={
        //   <Button
        //     component={RouterLink}
        //     href="#"
        //     variant="contained"
        //     startIcon={<Iconify icon="mingcute:add-line" />}
        //   >
        //     Thêm lớp mới
        //   </Button>
        // }
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card>
        <ClassTableToolbar
          filters={filters}
          onFilters={handleFilters}
          canReset={canReset}
          onResetFilters={handleResetFilters}
        />

        {canReset && (
          <TableSelectedAction
            dense={table.dense}
            numSelected={table.selected.length}
            rowCount={dataFiltered.length}
            onSelectAllRows={(checked) =>
              table.onSelectAllRows(
                checked,
                dataFiltered.map((row) => row.id)
              )
            }
            action={
              <Button color="error" onClick={handleDeleteRows}>
                Xóa
              </Button>
            }
          />
        )}

        <Scrollbar>
          <Table size={table.dense ? 'small' : 'medium'} sx={{ minWidth: 960 }}>
            <TableHeadCustom
              order={table.order}
              orderBy={table.orderBy}
              headCells={TABLE_HEAD}
              rowCount={dataFiltered.length}
              numSelected={table.selected.length}
              onSort={table.onSort}
              onSelectAllRows={(checked) =>
                table.onSelectAllRows(
                  checked,
                  dataFiltered.map((row) => row.id)
                )
              }
            />

            <TableBody>
              {dataInPage.map((row) => (
                <ClassTableRow
                  key={row.id}
                  row={row}
                  selected={table.selected.includes(row.id)}
                  onSelectRow={() => table.onSelectRow(row.id)}
                />
              ))}

              <TableEmptyRows
                height={table.dense ? 52 : 72}
                emptyRows={emptyRows(table.page, table.rowsPerPage, dataFiltered.length)}
              />

              <TableNoData notFound={notFound} />
            </TableBody>
          </Table>
        </Scrollbar>

        <TablePaginationCustom
          count={dataFiltered.length}
          page={table.page}
          rowsPerPage={table.rowsPerPage}
          onPageChange={table.onChangePage}
          onRowsPerPageChange={table.onChangeRowsPerPage}
          dense={table.dense}
          onChangeDense={table.onChangeDense}
        />
      </Card>
    </DashboardContent>
  );
}

function applyFilter({
  inputData,
  comparator,
  filters,
}: {
  inputData: ClassWithStats[];
  comparator: (a: any, b: any) => number;
  filters: ClassFilter;
}) {
  const { search, term, status } = filters;

  const stabilizedThis = inputData.map((el, index) => [el, index] as const);

  stabilizedThis.sort((a, b) => {
    const order = comparator(a[0], b[0]);
    if (order !== 0) return order;
    return a[1] - b[1];
  });

  inputData = stabilizedThis.map((el) => el[0]);

  if (search) {
    inputData = inputData.filter(
      (cls) =>
        cls.code.toLowerCase().indexOf(search.toLowerCase()) !== -1 ||
        cls.name.toLowerCase().indexOf(search.toLowerCase()) !== -1
    );
  }

  if (term) {
    inputData = inputData.filter((cls) => cls.term === term);
  }

  if (status) {
    inputData = inputData.filter((cls) => cls.status === status);
  }

  return inputData;
}
