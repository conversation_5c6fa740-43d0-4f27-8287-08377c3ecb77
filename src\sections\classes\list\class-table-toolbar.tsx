import type { ClassFilter } from 'src/types/academic';
import type { SelectChangeEvent } from '@mui/material/Select';

import dayjs from 'dayjs';
import { useCallback } from 'react';
import { usePopover } from 'minimal-shared/hooks';

import Stack from '@mui/material/Stack';
import Select from '@mui/material/Select';
import MenuItem from '@mui/material/MenuItem';
import Checkbox from '@mui/material/Checkbox';
import TextField from '@mui/material/TextField';
import InputLabel from '@mui/material/InputLabel';
import IconButton from '@mui/material/IconButton';
import FormControl from '@mui/material/FormControl';
import OutlinedInput from '@mui/material/OutlinedInput';
import InputAdornment from '@mui/material/InputAdornment';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import FormControlLabel from '@mui/material/FormControlLabel';

import { Iconify } from 'src/components/iconify';
import { CustomPopover } from 'src/components/custom-popover';

// ----------------------------------------------------------------------

type Props = {
  filters: ClassFilter;
  onFilters: (name: string, value: string) => void;
  canReset?: boolean;
  onResetFilters?: () => void;
};

export default function ClassTableToolbar({ filters, onFilters, canReset, onResetFilters }: Props) {
  const popover = usePopover();

  const handleFilterSearch = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      onFilters('search', event.target.value);
    },
    [onFilters]
  );

  const handleFilterSemester = useCallback(
    (event: SelectChangeEvent<string>) => {
      onFilters('semester', event.target.value);
    },
    [onFilters]
  );

  return (
    <Stack
      spacing={2}
      alignItems="center"
      direction={{ xs: 'column', md: 'row' }}
      sx={{ pr: { md: 1 }, p: 2 }}
    >
      <FormControl sx={{ flexShrink: 0, width: { xs: 1, md: 220 } }}>
        <DatePicker
          views={['year']}
          label="Năm"
          value={filters.year ? dayjs(`${filters.year}-01-01`) : null}
          onChange={(val) => onFilters('year', val ? String(val.year()) : '')}
          slotProps={{ textField: { fullWidth: true } }}
        />
      </FormControl>

      <FormControl sx={{ flexShrink: 0, width: { xs: 1, md: 160 } }}>
        <InputLabel shrink>Học kỳ</InputLabel>
        <Select
          value={String(filters.semester ?? '')}
          onChange={handleFilterSemester}
          input={<OutlinedInput label="Học kỳ" />}
          displayEmpty
          MenuProps={{ PaperProps: { sx: { maxHeight: 240 } } }}
          renderValue={(selected) => (selected ? selected : 'Tất cả')}
        >
          <MenuItem value="">Tất cả</MenuItem>
          <MenuItem value="1">Fall</MenuItem>
          <MenuItem value="2">Spring</MenuItem>
          <MenuItem value="3">Summer</MenuItem>
        </Select>
      </FormControl>

      <TextField
        fullWidth
        value={filters.search || ''}
        onChange={handleFilterSearch}
        placeholder="Tìm kiếm theo mã lớp hoặc tên học phần..."
        slotProps={{
          input: {
            startAdornment: (
              <InputAdornment position="start">
                <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />
              </InputAdornment>
            ),
          },
        }}
      />

      <IconButton onClick={popover.onOpen} sx={{ ml: 1 }}>
        <Iconify icon="eva:more-vertical-fill" />
      </IconButton>

      <CustomPopover
        open={popover.open}
        onClose={popover.onClose}
        anchorEl={popover.anchorEl}
        sx={{ width: 240 }}
      >
        <Stack spacing={1.5} sx={{ p: 2 }}>
          <FormControlLabel
            control={
              <Checkbox
                checked={!!filters.status}
                onChange={(e) => onFilters('status', e.target.checked ? 'active' : '')}
              />
            }
            label="Chỉ hiển thị lớp đang mở"
          />

          {canReset && (
            <IconButton color="error" onClick={onResetFilters}>
              <Iconify icon="solar:restart-bold" />
            </IconButton>
          )}
        </Stack>
      </CustomPopover>
    </Stack>
  );
}
