'use client';

import type { CreateAttendanceSessionForm } from 'src/types/academic';

import { z as zod } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';

import { createAttendanceSessionApi } from 'src/actions/ttu-attendance-adapter';

import { toast } from 'src/components/snackbar';
import { Form, Field } from 'src/components/hook-form';

export type CreateAttendanceSessionSchemaType = zod.infer<typeof CreateAttendanceSessionSchema>;

export const CreateAttendanceSessionSchema = zod.object({
  date: zod.string().min(1, { message: 'Vui lòng chọn ngày!' }),
  slot: zod.string().optional(),
  note: zod.string().optional(),
});

type Props = {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  classId: string;
};

export default function CreateAttendanceSessionDialog({
  open,
  onClose,
  onSuccess,
  classId,
}: Props) {
  const defaultValues: CreateAttendanceSessionSchemaType = {
    date: new Date().toISOString().split('T')[0],
    slot: '',
    note: '',
  };

  const methods = useForm<CreateAttendanceSessionSchemaType>({
    resolver: zodResolver(CreateAttendanceSessionSchema),
    defaultValues,
  });

  const {
    reset,
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const onSubmit = handleSubmit(async (data) => {
    try {
      const sessionData: CreateAttendanceSessionForm = {
        classId,
        date: new Date(data.date).toISOString(),
        slot: data.slot || undefined,
        note: data.note || undefined,
        locked: false,
      };

      await createAttendanceSessionApi(sessionData);

      toast.success('Tạo phiên điểm danh thành công!');
      reset();
      onSuccess();
    } catch (error) {
      console.error('Error creating attendance session:', error);
      toast.error('Có lỗi xảy ra khi tạo phiên điểm danh!');
    }
  });

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>Tạo phiên điểm danh mới</DialogTitle>

      <Form methods={methods} onSubmit={onSubmit}>
        <DialogContent>
          <Stack spacing={3} sx={{ pt: 1 }}>
            <Field.DatePicker
              name="date"
              label="Ngày điểm danh"
              slotProps={{
                textField: {
                  fullWidth: true,
                  required: true,
                },
              }}
            />

            <Field.Text
              name="note"
              label="Tên bài học"
              placeholder="Nhập Tên bài học cho phiên điểm danh..."
              multiline
              rows={3}
            />
          </Stack>
        </DialogContent>

        <DialogActions>
          <Button onClick={handleClose} color="inherit">
            Hủy
          </Button>
          <LoadingButton type="submit" variant="contained" loading={isSubmitting}>
            Tạo phiên điểm danh
          </LoadingButton>
        </DialogActions>
      </Form>
    </Dialog>
  );
}
