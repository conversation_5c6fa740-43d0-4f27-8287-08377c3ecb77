'use client';

import type { HistoryFilters as HistoryFiltersType } from 'src/actions/ttu-history-adapter';

import dayjs from 'dayjs';
import { useState, useEffect } from 'react';

import Stack from '@mui/material/Stack';
import Select from '@mui/material/Select';
import Button from '@mui/material/Button';
import MenuItem from '@mui/material/MenuItem';
import CardHeader from '@mui/material/CardHeader';
import InputLabel from '@mui/material/InputLabel';
import FormControl from '@mui/material/FormControl';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';

import { fetchClassOptions } from 'src/actions/ttu-report-adapter';

import { Iconify } from 'src/components/iconify';

type Props = {
  filters: HistoryFiltersType;
  onFiltersChange: (filters: Partial<HistoryFiltersType>) => void;
  loading?: boolean;
};

export function HistoryFilters({ filters, onFiltersChange, loading }: Props) {
  const [classOptions, setClassOptions] = useState<Array<{ value: string; label: string }>>([]);

  useEffect(() => {
    const loadClasses = async () => {
      if (filters.year) {
        const opts = await fetchClassOptions(
          String(filters.year),
          filters.semester ? String(filters.semester) : undefined
        );
        setClassOptions(opts);
      } else {
        setClassOptions([]);
      }
    };
    loadClasses();
  }, [filters.year, filters.semester]);

  const handleYearChange = (val: dayjs.Dayjs | null) => {
    const year = val ? String(val.year()) : '';
    onFiltersChange({
      year,
      classId: '', // Reset class when year changes
      semester: filters.semester, // Keep semester
    });
  };

  const handleSemesterChange = (value: string) => {
    onFiltersChange({
      semester: value,
      classId: '', // Reset class when semester changes
    });
  };

  const handleClassChange = (value: string) => {
    onFiltersChange({ classId: value });
  };

  const handleReset = () => {
    onFiltersChange({
      year: '',
      semester: '',
      classId: '',
    });
  };

  return (
    <>
      <CardHeader
        title="Lọc lịch sử điểm danh"
        action={
          <Button
            variant="outlined"
            startIcon={<Iconify icon="solar:refresh-bold" />}
            onClick={handleReset}
            disabled={loading}
          >
            Đặt lại
          </Button>
        }
      />

      <Stack direction={{ xs: 'column', md: 'row' }} spacing={2} sx={{ p: 2 }}>
        <FormControl sx={{ width: { xs: 1, md: 180 } }}>
          <DatePicker
            views={['year']}
            label="Năm học"
            value={filters.year ? dayjs(`${filters.year}-01-01`) : null}
            onChange={handleYearChange}
            slotProps={{
              textField: {
                fullWidth: true,
                disabled: loading,
              },
            }}
          />
        </FormControl>

        <FormControl sx={{ width: { xs: 1, md: 180 } }}>
          <InputLabel id="semester-label">Học kỳ</InputLabel>
          <Select
            labelId="semester-label"
            value={filters.semester ? String(filters.semester) : ''}
            onChange={(e) => handleSemesterChange(e.target.value)}
            label="Học kỳ"
            disabled={loading}
          >
            <MenuItem value="">Tất cả học kỳ</MenuItem>
            <MenuItem value="1">Học kỳ 1 (Fall)</MenuItem>
            <MenuItem value="2">Học kỳ 2 (Spring)</MenuItem>
            <MenuItem value="3">Học kỳ 3 (Summer)</MenuItem>
          </Select>
        </FormControl>

        <FormControl sx={{ width: { xs: 1, md: 320 } }}>
          <InputLabel id="class-label">Lớp học</InputLabel>
          <Select
            labelId="class-label"
            value={filters.classId ? String(filters.classId) : ''}
            onChange={(e) => handleClassChange(e.target.value)}
            label="Lớp học"
            disabled={loading || !filters.year}
            MenuProps={{ PaperProps: { sx: { maxHeight: 300 } } }}
          >
            <MenuItem value="">Tất cả lớp học</MenuItem>
            {classOptions.map((opt) => (
              <MenuItem key={opt.value} value={opt.value}>
                {opt.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Stack>
    </>
  );
}
