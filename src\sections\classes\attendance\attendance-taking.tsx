'use client';

import type { StudentWithGrades } from 'src/types/academic';

import { useState, useEffect, useCallback } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Chip from '@mui/material/Chip';
import Table from '@mui/material/Table';
import Stack from '@mui/material/Stack';
import Alert from '@mui/material/Alert';
import Button from '@mui/material/Button';
import Avatar from '@mui/material/Avatar';
import TableRow from '@mui/material/TableRow';
import TextField from '@mui/material/TextField';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import CardHeader from '@mui/material/CardHeader';
import Typography from '@mui/material/Typography';
import Autocomplete from '@mui/material/Autocomplete';
import InputAdornment from '@mui/material/InputAdornment';

import axios from 'src/lib/axios';
import { fetchAllClassStudents } from 'src/actions/ttu-student-adapter';
import { editAbsent, createAbsent, deleteAbsent } from 'src/actions/ttu-api';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';
import { TableHeadCustom } from 'src/components/table';

type AttendanceStatus = 'present' | 'absent' | 'permission';

type StudentAttendance = StudentWithGrades & {
  attendanceStatus: AttendanceStatus;
  absentId?: string;
};

type Props = {
  classId: string;
  sessionId: string;
};

export default function AttendanceTaking({ classId, sessionId }: Props) {
  const [students, setStudents] = useState<StudentAttendance[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchValue, setSearchValue] = useState<StudentAttendance | null>(null);
  const [searchInputValue, setSearchInputValue] = useState('');

  // Fetch existing absent records for this session
  const fetchAbsentData = useCallback(async () => {
    try {
      const response = await axios.get('/student/absent', {
        params: { attendance: sessionId },
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
          Expires: '0',
        },
      });

      const absentList = response.data?.data?.data || [];
      const absentMap = new Map();

      absentList.forEach((item: any) => {
        // Use multiple keys for mapping to ensure we find the match
        const originalStudentId = item.student._id;
        const studentEmail = item.student.email;
        const studentCode = item.student.student_id || item.student.code;

        const absentInfo = {
          absentId: String(item._id),
          is_permission: item.is_permission,
          status: item.is_permission === 1 ? 'permission' : 'absent',
        };

        // Map using multiple keys for better matching
        absentMap.set(originalStudentId, absentInfo);
        if (studentEmail) {
          absentMap.set(studentEmail, absentInfo);
        }
        if (studentCode) {
          absentMap.set(studentCode, absentInfo);
        }
      });

      return absentMap;
    } catch (error) {
      console.error('Failed to fetch absent data:', error);
      return new Map();
    }
  }, [sessionId]);

  const loadStudents = useCallback(async () => {
    try {
      setLoading(true);

      // Load students and absent data in parallel
      const [studentList, absentMap] = await Promise.all([
        fetchAllClassStudents(classId),
        fetchAbsentData(),
      ]);

      // Debug: Log student data structure
      console.log('Student data sample:', studentList[0]);

      const studentsWithAttendance: StudentAttendance[] = studentList.map((student) => {
        // Try multiple ways to find absent info
        const originalStudentId = Number(student._id);
        let absentInfo = absentMap.get(originalStudentId);

        // If not found by ID, try by email
        if (!absentInfo && student.email) {
          absentInfo = absentMap.get(student.email);
        }

        // If not found by email, try by student code
        if (!absentInfo && student.studentId) {
          absentInfo = absentMap.get(student.studentId);
        }

        if (absentInfo) {
          return {
            ...student,
            attendanceStatus: absentInfo.status as AttendanceStatus,
            absentId: absentInfo.absentId,
          };
        }

        // Default to present if no absent record
        return {
          ...student,
          attendanceStatus: 'present' as AttendanceStatus,
        };
      });

      setStudents(studentsWithAttendance);
    } catch (error) {
      console.error('Failed to load students:', error);
      toast.error('Không thể tải danh sách sinh viên');
    } finally {
      setLoading(false);
    }
  }, [classId, fetchAbsentData]);

  useEffect(() => {
    loadStudents();
  }, [loadStudents]);

  // Force refresh when sessionId changes
  useEffect(() => {
    if (sessionId) {
      fetchAbsentData();
    }
  }, [sessionId, fetchAbsentData]);

  const handleAttendanceChange = async (studentId: string, status: AttendanceStatus) => {
    const student = students.find((s) => s._id === studentId);
    if (!student) return;

    // Store original state for rollback
    const originalStatus = student.attendanceStatus;
    const originalAbsentId = student.absentId;

    try {
      // Update UI immediately for better UX (optimistic update)
      setStudents((prev) =>
        prev.map((s) =>
          s._id === studentId
            ? {
                ...s,
                attendanceStatus: status,
                // Keep absentId for now, will update after API call
              }
            : s
        )
      );

      let newAbsentId: string | undefined;

      if (status === 'present') {
        // If changing to present, delete absent record if exists
        if (student.absentId) {
          await deleteAbsent(student.absentId);
          newAbsentId = undefined;
          toast.success('Đã đánh dấu sinh viên có mặt');
        }
      } else if (student.absentId) {
        // If student already has absent record, update it
        const updateData = {
          is_permission: status === 'permission' ? 1 : 2,
        };

        await editAbsent(student.absentId, updateData);
        newAbsentId = student.absentId;
        toast.success(`Đã cập nhật trạng thái ${status === 'permission' ? 'có phép' : 'vắng'}`);
      } else {
        // If student doesn't have absent record, create new one
        const absentData = {
          class: classId,
          student: studentId,
          attendance: sessionId,
          is_permission: status === 'permission' ? 1 : 2,
        };

        const response = await createAbsent(absentData);
        newAbsentId = (response as any)?.data?._id;
        toast.success(`Đã đánh dấu sinh viên ${status === 'permission' ? 'có phép' : 'vắng'}`);
      }

      // Update UI with correct absentId after successful API call
      setStudents((prev) =>
        prev.map((s) =>
          s._id === studentId
            ? {
                ...s,
                attendanceStatus: status,
                absentId: newAbsentId,
              }
            : s
        )
      );
    } catch (error) {
      console.error('Failed to update attendance:', error);
      toast.error('Có lỗi xảy ra khi cập nhật điểm danh');

      // Rollback UI changes on error
      setStudents((prev) =>
        prev.map((s) =>
          s._id === studentId
            ? {
                ...s,
                attendanceStatus: originalStatus,
                absentId: originalAbsentId,
              }
            : s
        )
      );
    }
  };

  const getStatusColor = (status: AttendanceStatus) => {
    switch (status) {
      case 'present':
        return 'success';
      case 'permission':
        return 'warning';
      case 'absent':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusLabel = (status: AttendanceStatus) => {
    switch (status) {
      case 'present':
        return 'Có mặt';
      case 'permission':
        return 'Có phép';
      case 'absent':
        return 'Vắng';
      default:
        return 'Chưa xác định';
    }
  };

  const getStatusIcon = (status: AttendanceStatus) => {
    switch (status) {
      case 'present':
        return 'eva:checkmark-circle-2-fill';
      case 'permission':
        return 'eva:clock-fill';
      case 'absent':
        return 'eva:close-circle-fill';
      default:
        return 'eva:question-mark-circle-fill';
    }
  };

  const stats = {
    total: students.length,
    present: students.filter((s) => s.attendanceStatus === 'present').length,
    absent: students.filter((s) => s.attendanceStatus === 'absent').length,
    permission: students.filter((s) => s.attendanceStatus === 'permission').length,
  };

  if (loading) {
    return (
      <Card>
        <CardHeader title="Đang tải danh sách sinh viên..." />
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader title="Điểm danh sinh viên" />

      <Alert severity="info" sx={{ m: 2, mb: 0 }}>
        <Typography variant="body2">
          <strong>Hướng dẫn:</strong> Nhấn vào các nút để đánh dấu trạng thái điểm danh. Mặc định
          tất cả sinh viên được đánh dấu &quot;Có mặt&quot;.
        </Typography>
      </Alert>

      {/* Search Autocomplete */}
      <Box sx={{ p: 2, pb: 0 }}>
        <Autocomplete
          value={searchValue}
          onChange={(_event, newValue) => {
            setSearchValue(newValue);
            if (newValue) {
              // Scroll to the selected student
              const element = document.getElementById(`student-${newValue._id}`);
              if (element) {
                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                // Highlight the row briefly
                element.style.backgroundColor = '#fff3cd';
                setTimeout(() => {
                  element.style.backgroundColor = '';
                }, 2000);
              }
            }
          }}
          inputValue={searchInputValue}
          onInputChange={(_event, newInputValue) => {
            setSearchInputValue(newInputValue);
          }}
          options={students}
          getOptionLabel={(option) => {
            const studentData = option as any;
            const name =
              option.fullName ||
              studentData.fullName ||
              `${studentData.first_name || ''} ${studentData.last_name || ''}`.trim() ||
              'Không có tên';
            return `${option.studentId} - ${name}`;
          }}
          renderOption={(props, option) => {
            const studentData = option as any;
            const name =
              option.fullName ||
              studentData.fullName ||
              `${studentData.first_name || ''} ${studentData.last_name || ''}`.trim() ||
              'Không có tên';
            const firstChar = name.charAt(0) || option.studentId?.charAt(0) || '?';

            return (
              <Box component="li" {...props}>
                <Avatar src={option.avatar} sx={{ width: 32, height: 32, mr: 2 }}>
                  {firstChar}
                </Avatar>
                <Box>
                  <Typography variant="body2">{name}</Typography>
                  <Typography variant="caption" color="text.secondary">
                    {option.studentId} • {option.email}
                  </Typography>
                </Box>
              </Box>
            );
          }}
          renderInput={(params) => (
            <TextField
              {...params}
              label="Tìm kiếm sinh viên"
              placeholder="Nhập tên hoặc MSSV để tìm kiếm..."
              slotProps={{
                input: {
                  ...params.InputProps,
                  startAdornment: (
                    <InputAdornment position="start">
                      <Iconify icon="eva:search-fill" />
                    </InputAdornment>
                  ),
                },
              }}
            />
          )}
          noOptionsText="Không tìm thấy sinh viên"
          clearOnBlur={false}
          selectOnFocus
          handleHomeEndKeys
        />
      </Box>

      <Stack direction="row" spacing={2} sx={{ p: 2 }}>
        <Chip label={`Tổng: ${stats.total}`} color="default" variant="outlined" />
        <Chip label={`Có mặt: ${stats.present}`} color="success" variant="outlined" />
        <Chip label={`Có phép: ${stats.permission}`} color="warning" variant="outlined" />
        <Chip label={`Vắng: ${stats.absent}`} color="error" variant="outlined" />
      </Stack>

      <Scrollbar>
        <Table size="small" sx={{ minWidth: 800 }}>
          <TableHeadCustom
            headCells={[
              { id: 'studentId', label: 'MSSV' },
              { id: 'student', label: 'Thông tin sinh viên' },
              { id: 'status', label: 'Trạng thái hiện tại' },
              { id: 'actions', label: 'Hành động' },
            ]}
            order={undefined as any}
            orderBy={undefined as any}
            rowCount={students.length}
            numSelected={0}
          />
          <TableBody>
            {students.map((student) => (
              <TableRow key={student._id} id={`student-${student._id}`}>
                <TableCell sx={{ whiteSpace: 'nowrap' }}>{student.studentId}</TableCell>
                <TableCell>
                  <Stack direction="row" alignItems="center" spacing={2}>
                    <Avatar
                      src={student.avatar}
                      alt={student.fullName}
                      sx={{ width: 40, height: 40 }}
                    >
                      {student.fullName.charAt(0)}
                    </Avatar>
                    <div>
                      <Typography variant="subtitle2" noWrap>
                        {student.fullName}
                      </Typography>
                      {student.email && (
                        <Typography variant="body2" color="text.secondary" noWrap>
                          {student.email}
                        </Typography>
                      )}
                    </div>
                  </Stack>
                </TableCell>
                <TableCell>
                  <Chip
                    icon={<Iconify icon={getStatusIcon(student.attendanceStatus)} />}
                    label={getStatusLabel(student.attendanceStatus)}
                    color={getStatusColor(student.attendanceStatus) as any}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Stack direction="row" spacing={1}>
                    <Button
                      size="small"
                      variant={student.attendanceStatus === 'present' ? 'contained' : 'outlined'}
                      color="success"
                      onClick={() => handleAttendanceChange(student._id, 'present')}
                    >
                      Có mặt
                    </Button>
                    <Button
                      size="small"
                      variant={student.attendanceStatus === 'permission' ? 'contained' : 'outlined'}
                      color="warning"
                      onClick={() => handleAttendanceChange(student._id, 'permission')}
                    >
                      Có phép
                    </Button>
                    <Button
                      size="small"
                      variant={student.attendanceStatus === 'absent' ? 'contained' : 'outlined'}
                      color="error"
                      onClick={() => handleAttendanceChange(student._id, 'absent')}
                    >
                      Vắng
                    </Button>
                  </Stack>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Scrollbar>
    </Card>
  );
}
