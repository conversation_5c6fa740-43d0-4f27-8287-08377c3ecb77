'use client';

import type { AttendanceSessionWithRecords } from 'src/types/academic';

import { useState } from 'react';

import Chip from '@mui/material/Chip';
import Table from '@mui/material/Table';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import Tooltip from '@mui/material/Tooltip';
import TableRow from '@mui/material/TableRow';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import CardHeader from '@mui/material/CardHeader';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import DialogContentText from '@mui/material/DialogContentText';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { deleteAttendanceSessionApi } from 'src/actions/ttu-attendance-adapter';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';

export default function AttendanceSessionList({
  sessions,
  classId,
  onSessionDeleted,
}: {
  sessions: AttendanceSessionWithRecords[];
  classId: string;
  onSessionDeleted?: () => void;
}) {
  const [deleteDialog, setDeleteDialog] = useState<{
    open: boolean;
    session: AttendanceSessionWithRecords | null;
  }>({ open: false, session: null });

  const handleDeleteClick = (session: AttendanceSessionWithRecords) => {
    setDeleteDialog({ open: true, session });
  };

  const handleDeleteConfirm = async () => {
    if (!deleteDialog.session) return;

    try {
      await deleteAttendanceSessionApi(deleteDialog.session.id);
      toast.success('Xóa phiên điểm danh thành công');
      setDeleteDialog({ open: false, session: null });
      if (onSessionDeleted) {
        onSessionDeleted();
      }
    } catch (error) {
      console.error('Failed to delete session:', error);
      toast.error('Có lỗi xảy ra khi xóa phiên điểm danh');
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialog({ open: false, session: null });
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('vi-VN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
      });
    } catch {
      return dateString;
    }
  };

  const formatDateTime = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString('vi-VN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return dateString;
    }
  };

  const getStatusColor = (locked: boolean) => (locked ? 'success' : 'warning');

  const getStatusLabel = (locked: boolean) => (locked ? 'Đã chốt' : 'Đang mở');

  return (
    <>
      <CardHeader title="Các phiên điểm danh" />
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Thông tin phiên</TableCell>
            <TableCell>Trạng thái</TableCell>
            <TableCell>Thống kê điểm danh</TableCell>
            <TableCell>Người tạo</TableCell>
            <TableCell align="right">Hành động</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {sessions.length === 0 ? (
            <TableRow>
              <TableCell colSpan={5} align="center">
                <Typography variant="body2" color="text.secondary">
                  Chưa có phiên điểm danh nào
                </Typography>
              </TableCell>
            </TableRow>
          ) : (
            sessions.map((session) => (
              <TableRow key={session.id}>
                <TableCell>
                  <Stack spacing={0.5}>
                    <Typography variant="subtitle2">
                      {session.title || session.note || 'Điểm danh'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {formatDate(session.date)}
                      {session.slot && ` - Ca ${session.slot}`}
                    </Typography>
                  </Stack>
                </TableCell>
                <TableCell>
                  <Chip
                    label={getStatusLabel(session.locked ?? false)}
                    color={getStatusColor(session.locked ?? false) as any}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Stack direction="row" spacing={1}>
                    <Chip
                      label={`Có mặt: ${session.presentCount ?? 0}`}
                      color="success"
                      variant="outlined"
                      size="small"
                    />
                    <Chip
                      label={`Vắng: ${session.absentCount ?? 0}`}
                      color="error"
                      variant="outlined"
                      size="small"
                    />
                    <Chip
                      label={`Tổng: ${session.totalStudents ?? 0}`}
                      color="default"
                      variant="outlined"
                      size="small"
                    />
                  </Stack>
                </TableCell>
                <TableCell>
                  <Stack spacing={0.5}>
                    <Typography variant="body2">{session.createdBy || 'Hệ thống'}</Typography>
                    {session.createdAt && (
                      <Typography variant="caption" color="text.secondary">
                        {formatDateTime(session.createdAt)}
                      </Typography>
                    )}
                  </Stack>
                </TableCell>
                <TableCell align="right">
                  <Stack direction="row" spacing={1}>
                    <Tooltip title="Xem chi tiết / Điểm danh">
                      <IconButton
                        component={RouterLink}
                        href={paths.dashboard.classes.attendanceSession(classId, session.id)}
                        color="primary"
                        size="small"
                      >
                        <Iconify icon="solar:eye-bold" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Xóa phiên điểm danh">
                      <IconButton
                        onClick={() => handleDeleteClick(session)}
                        color="error"
                        size="small"
                      >
                        <Iconify icon="solar:trash-bin-trash-bold" />
                      </IconButton>
                    </Tooltip>
                  </Stack>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialog.open} onClose={handleDeleteCancel} maxWidth="sm" fullWidth>
        <DialogTitle>Xác nhận xóa phiên điểm danh</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Bạn có chắc chắn muốn xóa phiên điểm danh ngày{' '}
            <strong>
              {deleteDialog.session && formatDate(deleteDialog.session.date)}
              {deleteDialog.session?.slot && ` - Ca ${deleteDialog.session.slot}`}
            </strong>
            ?
            <br />
            <br />
            <strong>Lưu ý:</strong> Hành động này không thể hoàn tác và sẽ xóa tất cả dữ liệu điểm
            danh của phiên này.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel} color="inherit">
            Hủy
          </Button>
          <Button onClick={handleDeleteConfirm} color="error" variant="contained">
            Xóa
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
