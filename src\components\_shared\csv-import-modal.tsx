'use client';

import { useState, useCallback } from 'react';

import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import Typography from '@mui/material/Typography';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import LoadingButton from '@mui/lab/LoadingButton';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { Upload } from 'src/components/upload';

// ----------------------------------------------------------------------

type Props = {
  open: boolean;
  onClose: () => void;
  onSuccess: (data: any[]) => void;
  title?: string;
  description?: string;
  sampleData?: string[][];
};

export function CSVImportModal({
  open,
  onClose,
  onSuccess,
  title = 'Import CSV',
  description = 'Chọn file CSV để import dữ liệu',
  sampleData,
}: Props) {
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);

  const handleDrop = useCallback((acceptedFiles: File[]) => {
    const selectedFile = acceptedFiles[0];
    if (selectedFile) {
      setFile(selectedFile);
    }
  }, []);

  const handleImport = async () => {
    if (!file) {
      toast.error('Vui lòng chọn file CSV!');
      return;
    }

    try {
      setLoading(true);

      // Mock CSV parsing - in real app, use a CSV parsing library
      const text = await file.text();
      const lines = text.split('\n');
      const data = lines.slice(1).map((line) => line.split(','));

      // Simulate processing time
      await new Promise((resolve) => setTimeout(resolve, 1000));

      onSuccess(data);
      toast.success('Import CSV thành công!');
      handleClose();
    } catch (error) {
      console.error('Error importing CSV:', error);
      toast.error('Có lỗi xảy ra khi import CSV!');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setFile(null);
    onClose();
  };

  const downloadSample = () => {
    if (!sampleData) return;

    const csvContent = sampleData.map((row) => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'sample.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>{title}</DialogTitle>

      <DialogContent>
        <Stack spacing={3} sx={{ pt: 1 }}>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            {description}
          </Typography>

          {sampleData && (
            <Box>
              <Button
                variant="outlined"
                size="small"
                startIcon={<Iconify icon="solar:download-bold" />}
                onClick={downloadSample}
                sx={{ mb: 2 }}
              >
                Tải file mẫu
              </Button>
            </Box>
          )}

          <Upload
            onDrop={handleDrop}
            accept={{
              'text/csv': ['.csv'],
              'application/vnd.ms-excel': ['.xls'],
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
            }}
            placeholder={
              <Stack spacing={1} alignItems="center">
                <Iconify icon="eva:cloud-upload-fill" width={40} />
                <Typography variant="body2">Kéo thả file CSV hoặc click để chọn</Typography>
                <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                  Hỗ trợ: .csv, .xls, .xlsx
                </Typography>
              </Stack>
            }
          />

          {file && (
            <Box
              sx={{
                p: 2,
                borderRadius: 1,
                bgcolor: 'background.neutral',
                border: (theme) => `1px solid ${theme.vars.palette.divider}`,
              }}
            >
              <Stack direction="row" alignItems="center" spacing={2}>
                <Iconify icon="eva:file-text-fill" width={24} />
                <Box sx={{ flexGrow: 1, minWidth: 0 }}>
                  <Typography variant="subtitle2" noWrap>
                    {file.name}
                  </Typography>
                  <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                    {(file.size / 1024).toFixed(1)} KB
                  </Typography>
                </Box>
                <Button size="small" color="error" onClick={() => setFile(null)}>
                  Xóa
                </Button>
              </Stack>
            </Box>
          )}
        </Stack>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} color="inherit">
          Hủy
        </Button>
        <LoadingButton
          variant="contained"
          loading={loading}
          onClick={handleImport}
          disabled={!file}
        >
          Import
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
}
