import { useRef, useCallback, useEffect } from 'react';

export interface UseDragScrollOptions {
  scrollSpeed?: number;
  disabled?: boolean;
}

export function useDragScroll(options: UseDragScrollOptions = {}) {
  const { scrollSpeed = 2, disabled = false } = options;

  const scrollRef = useRef<HTMLDivElement>(null);
  const isDragging = useRef(false);
  const startX = useRef(0);
  const startY = useRef(0);
  const scrollLeft = useRef(0);
  const scrollTop = useRef(0);

  const getScrollElement = useCallback(() => {
    if (!scrollRef.current) return null;

    // Check if it's a SimpleBar component
    const simpleBarContent = scrollRef.current.querySelector(
      '.simplebar-content-wrapper'
    ) as HTMLElement;
    if (simpleBarContent) return simpleBarContent;

    // Otherwise use the element itself
    return scrollRef.current;
  }, []);

  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      if (disabled) return;

      const scrollElement = getScrollElement();
      if (!scrollElement) return;

      isDragging.current = true;
      startX.current = e.pageX;
      startY.current = e.pageY;
      scrollLeft.current = scrollElement.scrollLeft;
      scrollTop.current = scrollElement.scrollTop;

      // Change cursor to grabbing
      document.body.style.cursor = 'grabbing';
      document.body.style.userSelect = 'none';

      // Prevent text selection
      e.preventDefault();
    },
    [disabled, getScrollElement]
  );

  const handleMouseMove = useCallback(
    (e: React.MouseEvent) => {
      if (disabled || !isDragging.current) return;

      const scrollElement = getScrollElement();
      if (!scrollElement) return;

      e.preventDefault();

      const x = e.pageX;
      const y = e.pageY;
      const walkX = (x - startX.current) * scrollSpeed;
      const walkY = (y - startY.current) * scrollSpeed;

      scrollElement.scrollLeft = scrollLeft.current - walkX;
      scrollElement.scrollTop = scrollTop.current - walkY;
    },
    [disabled, scrollSpeed, getScrollElement]
  );

  const handleMouseUp = useCallback(() => {
    if (disabled) return;

    isDragging.current = false;
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
  }, [disabled]);

  const handleMouseLeave = useCallback(() => {
    if (disabled) return;

    isDragging.current = false;
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
  }, [disabled]);

  // Global mouse event handlers for better drag experience
  const handleGlobalMouseMove = useCallback(
    (e: MouseEvent) => {
      if (disabled || !isDragging.current) return;

      const scrollElement = getScrollElement();
      if (!scrollElement) return;

      e.preventDefault();

      const x = e.pageX;
      const y = e.pageY;
      const walkX = (x - startX.current) * scrollSpeed;
      const walkY = (y - startY.current) * scrollSpeed;

      scrollElement.scrollLeft = scrollLeft.current - walkX;
      scrollElement.scrollTop = scrollTop.current - walkY;
    },
    [disabled, scrollSpeed, getScrollElement]
  );

  const handleGlobalMouseUp = useCallback(() => {
    if (disabled) return;

    if (isDragging.current) {
      isDragging.current = false;
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    }
  }, [disabled]);

  // Add global event listeners
  useEffect(() => {
    if (disabled) return undefined;

    document.addEventListener('mousemove', handleGlobalMouseMove);
    document.addEventListener('mouseup', handleGlobalMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove);
      document.removeEventListener('mouseup', handleGlobalMouseUp);
    };
  }, [disabled, handleGlobalMouseMove, handleGlobalMouseUp]);

  return {
    ref: scrollRef,
    isDragging: isDragging.current,
    handlers: {
      onMouseDown: handleMouseDown,
      onMouseMove: handleMouseMove,
      onMouseUp: handleMouseUp,
      onMouseLeave: handleMouseLeave,
    },
  };
}
