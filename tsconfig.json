{
  "compilerOptions": {
    /* Bundler */
    "baseUrl": ".",
    "module": "esnext",
    "jsx": "preserve",
    "allowJs": true,
    "resolveJsonModule": true,

    /* Build */
    "target": "ES2017",
    "lib": ["dom", "dom.iterable", "esnext"],
    "moduleResolution": "bundler",
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
    "incremental": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "isolatedModules": true,

    /* Linting */
    "strict": true,
    "noEmit": true,
    "strictNullChecks": true,

    /* Plugins */
    "plugins": [{ "name": "next" }]
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules", "src/sections/_examples/**/*"]
}
