'use client';

import type { ReportData } from 'src/actions/ttu-report-adapter';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Chip from '@mui/material/Chip';
import Table from '@mui/material/Table';
import Alert from '@mui/material/Alert';
import TableRow from '@mui/material/TableRow';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import CardHeader from '@mui/material/CardHeader';
import Typography from '@mui/material/Typography';
import AlertTitle from '@mui/material/AlertTitle';

import { useDragScroll } from 'src/hooks/use-drag-scroll';

import { Scrollbar } from 'src/components/scrollbar';

type Props = {
  reportData: ReportData;
};

export default function AttendanceReportTable({ reportData }: Props) {
  const { title, headers, rows } = reportData;

  // Use drag scroll hook
  const dragScroll = useDragScroll({
    scrollSpeed: 2,
    disabled: false,
  });

  if (!headers.length || !rows.length) {
    return (
      <Card>
        <CardHeader title="Báo cáo điểm danh" />
        <Box sx={{ p: 3 }}>
          <Typography variant="body2" color="text.secondary" align="center">
            Không có dữ liệu báo cáo
          </Typography>
        </Box>
      </Card>
    );
  }

  const getAttendanceColor = (value: string) => {
    switch (value) {
      case 'P':
        return 'success';
      case 'A':
        return 'error';
      case 'R/A':
        return 'warning';
      case 'L':
        return 'info';
      default:
        return 'default';
    }
  };

  const getAttendanceLabel = (value: string) => {
    switch (value) {
      case 'P':
        return 'Có mặt';
      case 'A':
        return 'Vắng';
      case 'R/A':
        return 'Có phép';
      case 'L':
        return 'Trễ';
      default:
        return value;
    }
  };

  const isAttendanceColumn = (index: number) =>
    // Based on API response structure:
    // ["No.", "Full name", "Student ID", "Email", "Total days", "Total absent days", "Absent percentage", "Banned from final exam"]
    // Attendance sessions would be between Email and Total days (if any)
    // For now, no attendance sessions in this format
    false;
  const isPercentageColumn = (index: number) =>
    // "Absent percentage" column
    headers[index]?.toLowerCase().includes('percentage') ||
    headers[index]?.toLowerCase().includes('%');
  const isBannedColumn = (index: number) =>
    // "Banned from final exam" column
    headers[index]?.toLowerCase().includes('banned') ||
    headers[index]?.toLowerCase().includes('exam');
  const formatPercentage = (value: string) => {
    const num = parseFloat(value);
    if (isNaN(num)) return value;
    return `${num.toFixed(1)}%`;
  };

  const getBannedColor = (value: string) =>
    value.toLowerCase() === 'yes' || value.toLowerCase() === 'có' ? 'error' : 'success';

  const getBannedLabel = (value: string) => {
    const lower = value.toLowerCase();
    if (lower === 'yes' || lower === 'có') return 'Cấm thi';
    if (lower === 'no' || lower === 'không') return 'Được thi';
    return value;
  };

  return (
    <Card>
      <CardHeader title={title || 'Báo cáo điểm danh'} />

      <Alert severity="info" sx={{ m: 2 }}>
        <AlertTitle>Chú thích</AlertTitle>
        <Typography variant="body2">
          <strong>P</strong>: Present (Có mặt)
          <br />
          <strong>R/A</strong>: Request for Absence (Xin vắng mặt)
          <br />
          <strong>A</strong>: Absent (Vắng mặt)
        </Typography>
        <Typography variant="body2" sx={{ mt: 1, fontStyle: 'italic' }}>
          Cấm thi nếu tỷ lệ vắng &gt; 20%. Hover vào tiêu đề cột để xem tên đầy đủ.
        </Typography>
      </Alert>

      <Scrollbar
        ref={dragScroll.ref}
        {...dragScroll.handlers}
        sx={{
          cursor: 'grab',
          '&:active': {
            cursor: 'grabbing',
          },
          '&:hover': {
            '& .simplebar-scrollbar': {
              opacity: 1,
            },
          },
          // Add visual hint for drag functionality
          position: 'relative',
          '&::before': {
            content: '"🖱️ Kéo để cuộn"',
            position: 'absolute',
            top: -15,
            right: 8,
            fontSize: '12px',
            color: 'text.secondary',
            backgroundColor: 'background.paper',
            padding: '2px 6px',
            borderRadius: 1,
            zIndex: 1,
            opacity: 0.7,
            pointerEvents: 'none',
          },
        }}
      >
        <Table size="small" sx={{ minWidth: 800 }}>
          <TableHead>
            <TableRow>
              {headers.map((header, index) => {
                // Shorten long header text
                let displayHeader = header;
                if (typeof header === 'string') {
                  if (header.toLowerCase().includes('banned from final exam')) {
                    displayHeader = 'Cấm thi';
                  } else if (header.toLowerCase().includes('absent percentage')) {
                    displayHeader = 'Tỷ lệ vắng (%)';
                  } else if (header.toLowerCase().includes('total absent days')) {
                    displayHeader = 'Tổng ngày vắng';
                  } else if (header.toLowerCase().includes('total days')) {
                    displayHeader = 'Tổng ngày học';
                  } else if (header.toLowerCase().includes('student id')) {
                    displayHeader = 'MSSV';
                  } else if (header.toLowerCase().includes('full name')) {
                    displayHeader = 'Họ và tên';
                  } else if (header.toLowerCase().includes('no.')) {
                    displayHeader = 'STT';
                  }
                }

                return (
                  <TableCell
                    key={index}
                    sx={{
                      fontWeight: 'bold',
                      backgroundColor: 'background.neutral',
                      position: index <= 2 ? 'sticky' : 'static',
                      left: index === 0 ? 0 : index === 1 ? 80 : index === 2 ? 280 : 'auto',
                      zIndex: index <= 2 ? 10 : 1,
                      minWidth: index === 0 ? 80 : index === 1 ? 200 : index === 2 ? 150 : 120,
                      maxWidth: index === headers.length - 1 ? 120 : 'none', // Limit banned column width
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                    }}
                    title={header} // Show full text on hover
                  >
                    {displayHeader}
                  </TableCell>
                );
              })}
            </TableRow>
          </TableHead>
          <TableBody>
            {rows.map((row, rowIndex) => (
              <TableRow key={rowIndex} hover>
                {row.map((cell, cellIndex) => (
                  <TableCell
                    key={cellIndex}
                    sx={{
                      position: cellIndex <= 2 ? 'sticky' : 'static',
                      left:
                        cellIndex === 0 ? 0 : cellIndex === 1 ? 80 : cellIndex === 2 ? 280 : 'auto',
                      backgroundColor: cellIndex <= 2 ? 'background.paper' : 'transparent',
                      zIndex: cellIndex <= 2 ? 9 : 1,
                      minWidth:
                        cellIndex === 0 ? 80 : cellIndex === 1 ? 200 : cellIndex === 2 ? 150 : 120,
                    }}
                  >
                    {isAttendanceColumn(cellIndex) ? (
                      <Chip
                        label={getAttendanceLabel(cell)}
                        color={getAttendanceColor(cell) as any}
                        size="small"
                        variant="outlined"
                      />
                    ) : isPercentageColumn(cellIndex) ? (
                      <Typography
                        variant="body2"
                        color={parseFloat(cell) > 20 ? 'error.main' : 'text.primary'}
                        fontWeight={parseFloat(cell) > 20 ? 'bold' : 'normal'}
                      >
                        {formatPercentage(cell)}
                      </Typography>
                    ) : isBannedColumn(cellIndex) ? (
                      <Chip
                        label={getBannedLabel(cell)}
                        color={getBannedColor(cell) as any}
                        size="small"
                      />
                    ) : (
                      <Typography
                        variant="body2"
                        sx={{
                          maxWidth: cellIndex === 1 ? 200 : cellIndex === 3 ? 250 : 'none',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                        }}
                        title={String(cell)}
                      >
                        {cell}
                      </Typography>
                    )}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Scrollbar>
    </Card>
  );
}
