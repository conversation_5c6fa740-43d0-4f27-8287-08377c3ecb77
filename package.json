{"name": "@ttu/class-management", "version": "1.0.0", "description": "<PERSON><PERSON> thống điểm danh và nhập điểm cho giảng viên trường Đại học <PERSON>ân <PERSON>ạo, ph<PERSON><PERSON> triển bằng Next.js và TypeScript.", "author": "Ban IT - <PERSON><PERSON><PERSON> Tạo", "private": true, "scripts": {"dev": "next dev -p 8082", "start": "next start -p 8082", "build": "next build", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "lint:print": "npx eslint --print-config eslint.config.mjs > eslint-show-config.json", "fm:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx}\"", "fm:fix": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "fix:all": "npm run lint:fix && npm run fm:fix", "clean": "rm -rf node_modules .next out dist build", "re:dev": "yarn clean && yarn install && yarn dev", "re:build": "yarn clean && yarn install && yarn build", "re:build-npm": "npm run clean && npm install && npm run build", "tsc:dev": "yarn dev & yarn tsc:watch", "tsc:watch": "tsc --noEmit --watch", "tsc:print": "npx tsc --showConfig"}, "engines": {"node": ">=20"}, "packageManager": "yarn@1.22.22", "dependencies": {"@auth0/auth0-react": "^2.2.4", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource-variable/dm-sans": "^5.1.1", "@fontsource-variable/inter": "^5.1.1", "@fontsource-variable/nunito-sans": "^5.1.1", "@fontsource-variable/public-sans": "^5.1.2", "@fontsource/barlow": "^5.1.1", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@fullcalendar/timeline": "^6.1.15", "@hookform/resolvers": "^3.9.1", "@iconify/react": "^5.1.0", "@mui/lab": "^6.0.0-beta.21", "@mui/material": "^6.3.0", "@mui/material-nextjs": "^6.3.0", "@mui/x-data-grid": "^7.23.5", "@mui/x-date-pickers": "^7.23.3", "@mui/x-tree-view": "^7.23.2", "@react-pdf/renderer": "^4.1.6", "@supabase/supabase-js": "^2.47.10", "@tiptap/core": "^2.11.0", "@tiptap/extension-code-block": "^2.11.0", "@tiptap/extension-code-block-lowlight": "^2.11.0", "@tiptap/extension-image": "^2.11.0", "@tiptap/extension-link": "^2.11.0", "@tiptap/extension-placeholder": "^2.11.0", "@tiptap/extension-text-align": "^2.11.0", "@tiptap/extension-underline": "^2.11.0", "@tiptap/pm": "^2.11.0", "@tiptap/react": "^2.11.0", "@tiptap/starter-kit": "^2.11.0", "apexcharts": "^4.3.0", "autosuggest-highlight": "^3.3.4", "aws-amplify": "^6.11.0", "axios": "^1.7.9", "dayjs": "^1.11.13", "embla-carousel": "^8.5.1", "embla-carousel-auto-height": "^8.5.1", "embla-carousel-auto-scroll": "^8.5.1", "embla-carousel-autoplay": "^8.5.1", "embla-carousel-fade": "^8.5.1", "embla-carousel-react": "^8.5.1", "es-toolkit": "^1.31.0", "firebase": "^11.1.0", "framer-motion": "^11.15.0", "i18next": "^24.2.0", "i18next-browser-languagedetector": "^8.0.2", "i18next-resources-to-backend": "^1.2.1", "lowlight": "^3.3.0", "mapbox-gl": "^3.4.0", "minimal-shared": "^1.0.5", "mui-one-time-password-input": "^3.0.2", "next": "^14.2.22", "nprogress": "^0.2.0", "oidc-client-ts": "^3.3.0", "react": "^18.3.1", "react-apexcharts": "^1.7.0", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.2", "react-i18next": "^15.4.0", "react-joyride": "^2.9.3", "react-map-gl": "^7.1.8", "react-markdown": "^9.0.1", "react-organizational-chart": "^2.2.1", "react-phone-number-input": "^3.4.10", "rehype-highlight": "^7.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "simplebar-react": "^3.3.0", "sonner": "^1.7.1", "stylis": "^4.3.4", "stylis-plugin-rtl": "^2.1.1", "swr": "^2.3.0", "turndown": "^7.2.0", "yet-another-react-lightbox": "^3.21.7", "zod": "^3.24.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@svgr/webpack": "^8.1.0", "@types/autosuggest-highlight": "^3.2.3", "@types/node": "^22.10.3", "@types/nprogress": "^0.2.3", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/stylis": "^4.2.7", "@types/turndown": "^5.0.5", "@typescript-eslint/parser": "^8.19.0", "eslint": "^9.17.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-perfectionist": "^4.4.0", "eslint-plugin-react": "^7.37.3", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.14.0", "prettier": "^3.4.2", "typescript": "^5.7.2", "typescript-eslint": "^8.19.0"}, "peerDependencies": {"@types/mapbox-gl": "3.1.0"}}