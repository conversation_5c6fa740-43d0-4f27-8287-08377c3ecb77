import type { ClassWithStats, AttendanceSessionWithRecords } from 'src/types/academic';

import axios from 'src/lib/axios';

import {
  createAttendance,
  updateAttendance,
  deleteAttendance,
  getAttendanceHistory,
  getAttendanceDetails,
} from './ttu-api';

export async function fetchClassById(id: string): Promise<ClassWithStats | null> {
  // If there is an endpoint to get a single class, use it; otherwise get by year/semester and filter.
  // Placeholder: try /student/class?perPage=-1&_id
  const res = await axios.get('student/class', { params: { perPage: -1, _id: id } });
  const item = (res as any)?.data?.data?.data?.[0] ?? (res as any)?.data?.data?.[0];
  return item ? mapTtuClassToUi(item) : null;
}

export async function fetchAttendanceSessionsByClass(
  classId: string
): Promise<AttendanceSessionWithRecords[]> {
  try {
    // Use getAttendanceHistory with class filter
    const res = await getAttendanceHistory(1, 100, { _class: classId });
    const payload = (res as any)?.data;

    // API response structure: { code: 200, data: { total: number, data: [...] } }
    const responseData = payload?.data || payload;
    const items = responseData?.data || responseData?.items || [];

    return items.map(mapTtuAttendanceToUi);
  } catch (error) {
    console.error('Failed to fetch attendance sessions:', error);
    return [];
  }
}

// Map UI form -> TTU API payload
export async function createAttendanceSessionApi(data: any) {
  const payload = {
    class: data.classId ?? data.class ?? '',
    date: data.date?.split('T')[0] ?? data.date, // API expects YYYY-MM-DD
    content: data.note ?? data.content ?? '',
  };
  return createAttendance(payload);
}

export async function updateAttendanceSessionApi(id: string, data: any) {
  return updateAttendance(id, data);
}

export async function deleteAttendanceSessionApi(id: string) {
  return deleteAttendance(id);
}

export async function fetchAttendanceDetails(sessionId: string) {
  const res = await getAttendanceDetails(sessionId);
  const data = (res as any)?.data?.data ?? (res as any)?.data ?? [];

  // Ensure we always return an array
  return Array.isArray(data) ? data : [];
}

export function mapTtuClassToUi(raw: any): ClassWithStats & { year?: number; semester?: number } {
  const id = `${raw._id ?? raw.id ?? raw.class_id ?? raw.code}`;
  return {
    id,
    code: String(raw.code ?? raw.class_code ?? id),
    name: String(raw.name ?? raw.class_name ?? raw.name_vn ?? 'Lớp học'),
    term: String(raw.term ?? raw.semester ?? ''),
    size: Number(raw.size ?? raw.total_students ?? raw.students_count ?? 0),
    totalStudents: Number(raw.size ?? raw.total_students ?? raw.students_count ?? 0),
    nextSessionAt: raw.nextSessionAt ?? raw.next_session_at ?? undefined,
    status: raw.status as any,
    // Add year and semester from API response
    year: raw.year,
    semester: raw.semester,
  };
}

export function mapTtuAttendanceToUi(raw: any): AttendanceSessionWithRecords {
  const id = `${raw._id ?? raw.id ?? raw.attendance_id ?? ''}`;
  const date = raw.date ?? raw.attendance_date ?? raw.created_at ?? new Date().toISOString();
  const classInfo = raw.class || {};

  return {
    id,
    classId: String(
      raw.classId ?? raw.class_id ?? raw._class ?? classInfo._id ?? classInfo.id ?? ''
    ),
    date: String(date),
    slot: raw.slot ?? raw.time_slot ?? undefined,
    locked: Boolean(raw.locked ?? raw.is_locked ?? false),
    note: raw.note ?? raw.content ?? undefined,
    totalStudents: Number(raw.totalStudents ?? raw.total_students ?? raw.students_count ?? 0),
    presentCount: Number(raw.presentCount ?? raw.present_count ?? 0),
    absentCount: Number(raw.absentCount ?? raw.absent_count ?? 0),
    lateCount: Number(raw.lateCount ?? raw.late_count ?? 0),
    excusedCount: Number(raw.excusedCount ?? raw.excused_count ?? 0),
    // Additional fields for display
    title: raw.content || 'Điểm danh',
    createdAt: raw.created_at || '',
    createdBy: raw.created_by || 'Hệ thống',
    className: classInfo.name || 'Lớp học',
    classCode: classInfo.code || raw.code || '',
  };
}
