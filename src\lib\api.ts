// ----------------------------------------------------------------------
// Mock API for Academic Management System
// ----------------------------------------------------------------------

import type {
  Class,
  Student,
  GradeItem,
  ClassFilter,
  GradeFilter,
  ApiResponse,
  StudentFilter,
  ClassWithStats,
  DashboardStats,
  CreateClassForm,
  AttendanceFilter,
  AttendanceSession,
  StudentWithGrades,
  CreateStudentForm,
  CreateGradeItemForm,
  BulkGradeUpdateForm,
  GradeItemWithRecords,
  CreateAttendanceSessionForm,
  AttendanceSessionWithRecords,
} from 'src/types/academic';

// ----------------------------------------------------------------------
// Mock Data
// ----------------------------------------------------------------------

const MOCK_CLASSES: Class[] = [
  {
    id: '1',
    code: 'CS101',
    name: 'Lập trình cơ bản',
    term: '2024-2025-1',
    size: 45,
    nextSessionAt: '2024-12-20T08:00:00Z',
    teacherId: 'teacher1',
    room: 'A101',
    schedule: 'Thứ 2, 4 (7:30-9:30)',
    status: 'active',
    createdAt: '2024-08-15T00:00:00Z',
    updatedAt: '2024-12-15T00:00:00Z',
  },
  {
    id: '2',
    code: 'CS102',
    name: 'Cấu trúc dữ liệu và giải thuật',
    term: '2024-2025-1',
    size: 38,
    nextSessionAt: '2024-12-21T10:00:00Z',
    teacherId: 'teacher1',
    room: 'B205',
    schedule: 'Thứ 3, 5 (9:30-11:30)',
    status: 'active',
    createdAt: '2024-08-15T00:00:00Z',
    updatedAt: '2024-12-15T00:00:00Z',
  },
  {
    id: '3',
    code: 'CS201',
    name: 'Cơ sở dữ liệu',
    term: '2024-2025-1',
    size: 42,
    nextSessionAt: '2024-12-22T13:30:00Z',
    teacherId: 'teacher1',
    room: 'C301',
    schedule: 'Thứ 6 (13:30-16:30)',
    status: 'active',
    createdAt: '2024-08-15T00:00:00Z',
    updatedAt: '2024-12-15T00:00:00Z',
  },
];

const MOCK_STUDENTS: Student[] = [
  {
    _id: '1',
    studentId: '2021001',
    fullName: 'Nguyễn Văn An',
    email: '<EMAIL>',
    phone: '0901234567',
    status: 'active',
    enrolledAt: '2024-08-15T00:00:00Z',
  },
  {
    _id: '2',
    studentId: '2021002',
    fullName: 'Trần Thị Bình',
    email: '<EMAIL>',
    phone: '0901234568',
    status: 'active',
    enrolledAt: '2024-08-15T00:00:00Z',
  },
  {
    _id: '3',
    studentId: '2021003',
    fullName: 'Lê Văn Cường',
    email: '<EMAIL>',
    phone: '0901234569',
    status: 'active',
    enrolledAt: '2024-08-15T00:00:00Z',
  },
];

const MOCK_ATTENDANCE_SESSIONS: AttendanceSession[] = [
  {
    id: '1',
    classId: '1',
    date: '2024-12-16T00:00:00Z',
    slot: '1-2',
    note: 'Buổi học bình thường',
    locked: true,
    totalStudents: 45,
    presentCount: 42,
    absentCount: 2,
    lateCount: 1,
    excusedCount: 0,
    createdAt: '2024-12-16T07:00:00Z',
    updatedAt: '2024-12-16T09:30:00Z',
  },
  {
    id: '2',
    classId: '1',
    date: '2024-12-18T00:00:00Z',
    slot: '1-2',
    note: 'Ôn tập giữa kỳ',
    locked: false,
    totalStudents: 45,
    presentCount: 40,
    absentCount: 3,
    lateCount: 2,
    excusedCount: 0,
    createdAt: '2024-12-18T07:00:00Z',
    updatedAt: '2024-12-18T09:30:00Z',
  },
];

const MOCK_GRADE_ITEMS: GradeItem[] = [
  {
    id: '1',
    classId: '1',
    name: 'Điểm giữa kỳ',
    weight: 0.3,
    maxScore: 10,
    type: 'midterm',
    description: 'Kiểm tra giữa kỳ môn Lập trình cơ bản',
    isPublished: true,
    createdAt: '2024-09-01T00:00:00Z',
    updatedAt: '2024-11-15T00:00:00Z',
  },
  {
    id: '2',
    classId: '1',
    name: 'Điểm cuối kỳ',
    weight: 0.5,
    maxScore: 10,
    type: 'final',
    description: 'Thi cuối kỳ môn Lập trình cơ bản',
    isPublished: false,
    createdAt: '2024-09-01T00:00:00Z',
    updatedAt: '2024-12-01T00:00:00Z',
  },
  {
    id: '3',
    classId: '1',
    name: 'Chuyên cần',
    weight: 0.1,
    maxScore: 10,
    type: 'attendance',
    description: 'Điểm chuyên cần dựa trên tỷ lệ tham gia lớp',
    isPublished: true,
    createdAt: '2024-09-01T00:00:00Z',
    updatedAt: '2024-12-15T00:00:00Z',
  },
  {
    id: '4',
    classId: '1',
    name: 'Bài tập lớn',
    weight: 0.1,
    maxScore: 10,
    type: 'assignment',
    description: 'Dự án cuối kỳ',
    dueDate: '2024-12-25T23:59:59Z',
    isPublished: false,
    createdAt: '2024-09-01T00:00:00Z',
    updatedAt: '2024-12-01T00:00:00Z',
  },
];

// ----------------------------------------------------------------------
// Utility Functions
// ----------------------------------------------------------------------

const delay = (ms: number = 500) => new Promise((resolve) => setTimeout(resolve, ms));

const generateId = () => Math.random().toString(36).substr(2, 9);

// ----------------------------------------------------------------------
// API Functions
// ----------------------------------------------------------------------

// Classes
export async function getClasses(filter?: ClassFilter): Promise<ClassWithStats[]> {
  await delay();

  let filteredClasses = [...MOCK_CLASSES];

  if (filter?.term) {
    filteredClasses = filteredClasses.filter((c) => c.term === filter.term);
  }

  if (filter?.status) {
    filteredClasses = filteredClasses.filter((c) => c.status === filter.status);
  }

  if (filter?.search) {
    const search = filter.search.toLowerCase();
    filteredClasses = filteredClasses.filter(
      (c) => c.code.toLowerCase().includes(search) || c.name.toLowerCase().includes(search)
    );
  }

  return filteredClasses.map((cls) => ({
    ...cls,
    totalStudents: cls.size,
    attendanceRate: Math.random() * 0.2 + 0.8, // 80-100%
    lastAttendanceDate: '2024-12-18T00:00:00Z',
  }));
}

export async function getClass(id: string): Promise<ClassWithStats | null> {
  await delay();
  const cls = MOCK_CLASSES.find((c) => c.id === id);
  if (!cls) return null;

  return {
    ...cls,
    totalStudents: cls.size,
    attendanceRate: Math.random() * 0.2 + 0.8,
    lastAttendanceDate: '2024-12-18T00:00:00Z',
  };
}

export async function createClass(data: CreateClassForm): Promise<ApiResponse<Class>> {
  await delay();
  const newClass: Class = {
    ...data,
    id: generateId(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  MOCK_CLASSES.push(newClass);

  return {
    data: newClass,
    success: true,
    message: 'Tạo lớp học thành công',
  };
}

// Students
export async function getStudents(
  classId: string,
  filter?: StudentFilter
): Promise<StudentWithGrades[]> {
  await delay();

  let students = [...MOCK_STUDENTS];

  if (filter?.status) {
    students = students.filter((s) => s.status === filter.status);
  }

  if (filter?.search) {
    const search = filter.search.toLowerCase();
    students = students.filter(
      (s) =>
        s.studentId.toLowerCase().includes(search) ||
        s.fullName.toLowerCase().includes(search) ||
        s.email?.toLowerCase().includes(search)
    );
  }

  return students.map((student) => ({
    ...student,
    attendanceRate: Math.random() * 0.3 + 0.7, // 70-100%
    totalScore: Math.random() * 3 + 7, // 7-10
  }));
}

export async function createStudent(data: CreateStudentForm): Promise<ApiResponse<Student>> {
  await delay();
  const newStudent: Student = {
    ...data,
    _id: generateId(),
    enrolledAt: new Date().toISOString(),
  };

  MOCK_STUDENTS.push(newStudent);

  return {
    data: newStudent,
    success: true,
    message: 'Thêm sinh viên thành công',
  };
}

// Attendance
export async function getAttendanceSessions(
  classId: string,
  filter?: AttendanceFilter
): Promise<AttendanceSessionWithRecords[]> {
  await delay();

  let sessions = MOCK_ATTENDANCE_SESSIONS.filter((s) => s.classId === classId);

  if (filter?.dateFrom) {
    sessions = sessions.filter((s) => s.date >= filter.dateFrom!);
  }

  if (filter?.dateTo) {
    sessions = sessions.filter((s) => s.date <= filter.dateTo!);
  }

  return sessions.map((session) => ({
    ...session,
    records: [], // Sẽ load riêng khi cần
  }));
}

export async function createAttendanceSession(
  data: CreateAttendanceSessionForm
): Promise<ApiResponse<AttendanceSession>> {
  await delay();
  const newSession: AttendanceSession = {
    ...data,
    id: generateId(),
    totalStudents: 0,
    presentCount: 0,
    absentCount: 0,
    lateCount: 0,
    excusedCount: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  MOCK_ATTENDANCE_SESSIONS.push(newSession);

  return {
    data: newSession,
    success: true,
    message: 'Tạo phiên điểm danh thành công',
  };
}

// Grades
export async function getGradeItems(
  classId: string,
  filter?: GradeFilter
): Promise<GradeItemWithRecords[]> {
  await delay();

  let items = MOCK_GRADE_ITEMS.filter((item) => item.classId === classId);

  if (filter?.type) {
    items = items.filter((item) => item.type === filter.type);
  }

  if (filter?.isPublished !== undefined) {
    items = items.filter((item) => item.isPublished === filter.isPublished);
  }

  return items.map((item) => ({
    ...item,
    averageScore: Math.random() * 2 + 7, // 7-9
    submissionRate: Math.random() * 0.2 + 0.8, // 80-100%
  }));
}

export async function createGradeItem(data: CreateGradeItemForm): Promise<ApiResponse<GradeItem>> {
  await delay();
  const newItem: GradeItem = {
    ...data,
    id: generateId(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  MOCK_GRADE_ITEMS.push(newItem);

  return {
    data: newItem,
    success: true,
    message: 'Tạo hạng mục điểm thành công',
  };
}

export async function updateBulkGrades(data: BulkGradeUpdateForm): Promise<ApiResponse<boolean>> {
  await delay();

  // Mock implementation - in real app, this would update the database
  console.log('Updating bulk grades:', data);

  return {
    data: true,
    success: true,
    message: 'Cập nhật điểm thành công',
  };
}

// Dashboard
export async function getDashboardStats(): Promise<DashboardStats> {
  await delay();

  return {
    totalClasses: MOCK_CLASSES.length,
    totalStudents: MOCK_STUDENTS.length,
    upcomingSessions: 3,
    pendingGrades: 12,
    averageAttendanceRate: 0.87,
    recentActivities: [
      {
        id: '1',
        type: 'attendance',
        message: 'Điểm danh lớp CS101 - Lập trình cơ bản',
        timestamp: '2024-12-18T09:30:00Z',
        classId: '1',
        className: 'CS101',
      },
      {
        id: '2',
        type: 'grade',
        message: 'Nhập điểm giữa kỳ lớp CS102',
        timestamp: '2024-12-17T14:20:00Z',
        classId: '2',
        className: 'CS102',
      },
      {
        id: '3',
        type: 'class',
        message: 'Tạo lớp mới CS301 - Mạng máy tính',
        timestamp: '2024-12-16T10:15:00Z',
        classId: '4',
        className: 'CS301',
      },
    ],
  };
}
