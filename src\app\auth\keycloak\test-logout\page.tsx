'use client';

import { useState } from 'react';
import { 
  <PERSON>, 
  Typo<PERSON>, 
  <PERSON>, 
  But<PERSON>, 
  Alert, 
  TextField,
  Divider
} from '@mui/material';

import { CONFIG } from 'src/global-config';

// ----------------------------------------------------------------------

export default function KeycloakTestLogoutPage() {
  const [testUrl, setTestUrl] = useState('');

  const generateTestUrl = () => {
    // Create a simple logout URL without id_token_hint to test redirect URI
    const logoutUrl = `${CONFIG.keycloak.authority}/protocol/openid-connect/logout?post_logout_redirect_uri=${encodeURIComponent(CONFIG.keycloak.postLogoutRedirectUri)}`;
    setTestUrl(logoutUrl);
  };

  const testLogoutRedirect = () => {
    if (testUrl) {
      window.open(testUrl, '_blank');
    }
  };

  const currentPostLogoutUri = CONFIG.keycloak.postLogoutRedirectUri;
  const decodedUri = decodeURIComponent(currentPostLogoutUri);

  return (
    <Box sx={{ p: 3, maxWidth: 1000, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        Keycloak Logout URL Tester
      </Typography>

      <Alert severity="warning" sx={{ mb: 3 }}>
        <Typography variant="body2">
          This tool helps test the logout redirect URI configuration by creating a direct logout URL.
        </Typography>
      </Alert>

      {/* Current Configuration */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Current Configuration
        </Typography>
        <Box sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>
          <div><strong>Authority:</strong> {CONFIG.keycloak.authority}</div>
          <div><strong>Client ID:</strong> {CONFIG.keycloak.clientId}</div>
          <div><strong>Post Logout Redirect URI:</strong> {currentPostLogoutUri}</div>
          <div><strong>Decoded URI:</strong> {decodedUri}</div>
        </Box>
      </Paper>

      {/* Generate Test URL */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Test Logout URL
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          Click to generate a test logout URL without authentication token.
        </Typography>
        
        <Button 
          variant="contained" 
          onClick={generateTestUrl}
          sx={{ mb: 2 }}
        >
          Generate Test URL
        </Button>

        {testUrl && (
          <Box>
            <TextField
              fullWidth
              label="Test Logout URL"
              value={testUrl}
              multiline
              rows={3}
              sx={{ mb: 2 }}
              InputProps={{ readOnly: true }}
            />
            <Button 
              variant="outlined" 
              onClick={testLogoutRedirect}
            >
              Test This URL
            </Button>
          </Box>
        )}
      </Paper>

      {/* Expected Behavior */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Expected Behavior
        </Typography>
        <Typography variant="body2" paragraph>
          When you click "Test This URL":
        </Typography>
        <Box component="ol" sx={{ pl: 2 }}>
          <li>
            <Typography variant="body2">
              <strong>If configured correctly:</strong> You should be redirected to {currentPostLogoutUri}
            </Typography>
          </li>
          <li>
            <Typography variant="body2">
              <strong>If misconfigured:</strong> You'll see "We are sorry... Invalid redirect uri"
            </Typography>
          </li>
        </Box>
      </Paper>

      {/* Troubleshooting */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          If You See "Invalid redirect uri"
        </Typography>
        <Typography variant="body2" paragraph>
          The issue is in Keycloak client configuration. Follow these steps:
        </Typography>
        
        <Box component="ol" sx={{ pl: 2 }}>
          <li>
            <Typography variant="body2">
              Open Keycloak Admin Console: <code>http://localhost:8080/admin</code>
            </Typography>
          </li>
          <li>
            <Typography variant="body2">
              Navigate to: <strong>Clients</strong> → <strong>{CONFIG.keycloak.clientId}</strong> → <strong>Settings</strong>
            </Typography>
          </li>
          <li>
            <Typography variant="body2">
              Scroll down to <strong>"Access settings"</strong> section
            </Typography>
          </li>
          <li>
            <Typography variant="body2">
              In <strong>"Valid post logout redirect URIs"</strong> field, add:
              <Box sx={{ fontFamily: 'monospace', bgcolor: '#f5f5f5', p: 1, mt: 1, borderRadius: 1 }}>
                {currentPostLogoutUri}<br />
                {window.location.origin}/*
              </Box>
            </Typography>
          </li>
          <li>
            <Typography variant="body2">
              Click <strong>Save</strong>
            </Typography>
          </li>
          <li>
            <Typography variant="body2">
              Test the URL again
            </Typography>
          </li>
        </Box>
      </Paper>

      {/* Quick Actions */}
      <Paper sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Quick Actions
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <Button 
            variant="outlined" 
            onClick={() => window.open('http://localhost:8080/admin', '_blank')}
          >
            Keycloak Admin Console
          </Button>
          <Button 
            variant="outlined" 
            onClick={() => window.open('/auth/keycloak/debug', '_blank')}
          >
            Debug Page
          </Button>
          <Button 
            variant="outlined" 
            onClick={() => window.open('/auth/keycloak/config-check', '_blank')}
          >
            Config Check
          </Button>
        </Box>
      </Paper>

      {/* Direct Configuration URL */}
      <Paper sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Direct Configuration Link
        </Typography>
        <Typography variant="body2" paragraph>
          Direct link to client configuration (requires admin login):
        </Typography>
        <Button 
          variant="outlined" 
          onClick={() => window.open(`http://localhost:8080/admin/master/console/#/ttu/clients/${CONFIG.keycloak.clientId}/settings`, '_blank')}
        >
          Open Client Settings Directly
        </Button>
      </Paper>
    </Box>
  );
}
