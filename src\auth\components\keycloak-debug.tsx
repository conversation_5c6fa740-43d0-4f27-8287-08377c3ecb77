'use client';

import { useState, useEffect } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import CardContent from '@mui/material/CardContent';

import { CONFIG } from 'src/global-config';

import { validateKeycloakConfig } from '../context/keycloak/test-config';

// ----------------------------------------------------------------------

export function KeycloakDebug() {
  const [configValidation, setConfigValidation] = useState<any>(null);

  useEffect(() => {
    if (CONFIG.auth.method === 'keycloak') {
      const validation = validateKeycloakConfig();
      setConfigValidation(validation);
    }
  }, []);

  const handleTestConfig = () => {
    const validation = validateKeycloakConfig();
    setConfigValidation(validation);
    console.log('Keycloak config test:', validation);
  };

  if (CONFIG.auth.method !== 'keycloak' || process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <Card sx={{ m: 2, maxWidth: 600 }}>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          🔐 Keycloak Debug Info
        </Typography>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          This debug panel is only visible in development mode when using Keycloak auth.
        </Typography>

        <Button variant="outlined" onClick={handleTestConfig} sx={{ mb: 2 }}>
          Test Configuration
        </Button>

        {configValidation && (
          <Box>
            <Typography variant="subtitle2" sx={{ mb: 1 }}>
              Configuration Status: {configValidation.isValid ? '✅ Valid' : '❌ Invalid'}
            </Typography>

            {configValidation.errors.length > 0 && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="error" sx={{ mb: 1 }}>
                  Errors:
                </Typography>
                {configValidation.errors.map((error: string, index: number) => (
                  <Typography key={index} variant="body2" color="error" sx={{ ml: 2 }}>
                    • {error}
                  </Typography>
                ))}
              </Box>
            )}

            <Box>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Current Configuration:
              </Typography>
              <Box component="pre" sx={{ 
                fontSize: '0.75rem', 
                backgroundColor: 'grey.100', 
                p: 1, 
                borderRadius: 1,
                overflow: 'auto'
              }}>
                {JSON.stringify(configValidation.config, null, 2)}
              </Box>
            </Box>
          </Box>
        )}

        <Typography variant="caption" color="text.secondary" sx={{ mt: 2, display: 'block' }}>
          Check the browser console for detailed logs when testing configuration.
        </Typography>
      </CardContent>
    </Card>
  );
}
