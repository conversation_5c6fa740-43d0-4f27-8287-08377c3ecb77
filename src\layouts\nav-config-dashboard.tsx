import type { NavSectionProps } from 'src/components/nav-section';

import { paths } from 'src/routes/paths';

import { CONFIG } from 'src/global-config';

import { SvgColor } from 'src/components/svg-color';

// ----------------------------------------------------------------------

const icon = (name: string) => (
  <SvgColor src={`${CONFIG.assetsDir}/assets/icons/navbar/${name}.svg`} />
);

const ICONS = {
  job: icon('ic-job'),
  blog: icon('ic-blog'),
  chat: icon('ic-chat'),
  mail: icon('ic-mail'),
  user: icon('ic-user'),
  file: icon('ic-file'),
  lock: icon('ic-lock'),
  tour: icon('ic-tour'),
  order: icon('ic-order'),
  label: icon('ic-label'),
  blank: icon('ic-blank'),
  kanban: icon('ic-kanban'),
  folder: icon('ic-folder'),
  course: icon('ic-course'),
  banking: icon('ic-banking'),
  booking: icon('ic-booking'),
  invoice: icon('ic-invoice'),
  product: icon('ic-product'),
  calendar: icon('ic-calendar'),
  disabled: icon('ic-disabled'),
  external: icon('ic-external'),
  menuItem: icon('ic-menu-item'),
  ecommerce: icon('ic-ecommerce'),
  analytics: icon('ic-analytics'),
  dashboard: icon('ic-dashboard'),
  parameter: icon('ic-parameter'),
  calendarSearch: icon('ic-calendar-search'),
  group: icon('ic-group'),
  notebook: icon('ic-notebook'),
};

// ----------------------------------------------------------------------

export const navData: NavSectionProps['data'] = [
  /**
   * Overview
   */
  {
    subheader: 'Tổng quan',
    items: [{ title: 'Trang chủ', path: paths.dashboard.root, icon: ICONS.dashboard }],
  },
  /**
   * Academic Management
   */
  {
    subheader: 'Quản lý học tập',
    items: [
      { title: 'Quản lý học kỳ', path: paths.dashboard.semesters, icon: ICONS.calendar },
      { title: 'Danh sách lớp', path: paths.dashboard.classes.root, icon: ICONS.course },
      { title: 'Sinh viên', path: paths.dashboard.students, icon: ICONS.group },
      {
        title: 'Quản lý điểm',
        path: paths.dashboard.gradebook,
        icon: ICONS.notebook,
        children: [
          { title: 'Sổ điểm lớp', path: paths.dashboard.gradebook },
          { title: 'Nhập điểm', path: paths.dashboard.gradeEntry },
        ],
      },
    ],
  },
  /**
   * Attendance Management
   */
  {
    subheader: 'Quản lý điểm danh',
    items: [
      {
        title: 'Điểm danh',
        path: paths.dashboard.attendance,
        icon: ICONS.user,
        children: [
          { title: 'Tạo phiên điểm danh', path: paths.dashboard.attendance },
          { title: 'Lịch sử điểm danh', path: paths.dashboard.attendanceHistory },
        ],
      },
    ],
  },
  /**
   * Reports & Analytics
   */
  {
    subheader: 'Báo cáo & Thống kê',
    items: [
      {
        title: 'Báo cáo',
        path: paths.dashboard.reports,
        icon: ICONS.analytics,
        children: [
          { title: 'Báo cáo điểm danh', path: paths.dashboard.reports },
          {
            title: 'Báo cáo sinh viên',
            path: paths.dashboard.studentReports || paths.dashboard.reports,
          },
        ],
      },
    ],
  },
];
