import axios from 'src/lib/axios';

import {
  getReport,
  downloadReport,
  getAbsentSemester,
  getGradeComponents,
  getClass as getClassesByYear,
  getDataClass as getClassesByYearSemester,
} from './ttu-api';

export type ReportData = {
  title: string;
  headers: string[];
  rows: string[][];
};

export type ReportPreview = {
  // Define fields as needed by UI; keep as any for flexibility if backend varies
  raw: any;
};

export async function fetchClassOptions(year?: string, semester?: string) {
  if (!year && !semester) return [] as Array<{ value: string; label: string }>;
  const res = semester
    ? await getClassesByYearSemester(year!, semester!)
    : await getClassesByYear(year!);
  const payload = (res as any)?.data?.data?.data ?? (res as any)?.data?.data ?? [];

  return payload.map((item: any) => ({
    value: String(item._id ?? item.id),
    label: `${item.name_vn ?? item.name} - ${item.code}`,
  }));
}

export async function fetchAttendanceReport(classId: string): Promise<ReportData> {
  const res = await getReport(classId);
  const payload = (res as any)?.data;

  // API returns: { code: 200, message: "Success", data: { table: [...] } }
  const table = payload?.data?.table ?? payload?.table ?? [];

  if (table.length === 0) {
    return {
      title: 'Báo cáo điểm danh',
      headers: [],
      rows: [],
    };
  }

  // Structure: [title_row, headers_row, ...data_rows]
  // title_row: ["REGISTRAR OFFICE"]
  // headers_row: ["No.", "Full name", "Student ID", ...]
  // data_rows: [1, "Bùi Thị Bích Trăm", "2201062", ...]

  const title = table[0]?.[0] || 'Báo cáo điểm danh';
  const headers = table[1] || [];
  const rows = table.slice(2) || [];

  return {
    title,
    headers,
    rows,
  };
}

export async function fetchReportPreview(classId: string) {
  const res = await getReport(classId);
  return (res as any)?.data ?? {};
}

export async function downloadAttendanceReport(
  year: string | number,
  classId: string
): Promise<string> {
  try {
    const result = await downloadReport(year, classId);

    // API response structure: { code: 200, message: "Success", data: { directory: "...", fileName: "..." } }
    // Need to access result.data.data.directory (nested data)
    const responseData = result?.data?.data || result?.data;
    const directory = responseData?.directory;

    if (directory) {
      const downloadUrl = `https://apihr.ttu.edu.vn/v1/static/${directory}`;
      return downloadUrl;
    }

    throw new Error('Không nhận được đường dẫn file từ server');
  } catch (error) {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Không thể tạo file báo cáo');
  }
}

export function buildReportDownloadUrl(year: string, classId: string) {
  const base = '/api/student/attendance/report';
  const params = new URLSearchParams({ year, _class: classId });
  return `${base}?${params.toString()}`;
}

export async function triggerReportDownload(year: string, classId: string) {
  const url = buildReportDownloadUrl(year, classId);
  const res = await axios.get(url);
  return res.data ?? {};
}

// Helper function to get current academic year
export function getCurrentAcademicYear(): string {
  const now = new Date();

  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth() + 1; // 0-based month

  // Academic year typically starts in September
  if (currentMonth >= 9) {
    return currentYear.toString();
  } else {
    return (currentYear - 1).toString();
  }
}

// Helper function to get current semester
export function getCurrentSemester(): string {
  const now = new Date();
  const currentMonth = now.getMonth() + 1; // 0-based month

  if (currentMonth >= 9 || currentMonth <= 1) {
    return '1'; // Fall semester
  } else if (currentMonth >= 2 && currentMonth <= 6) {
    return '2'; // Spring semester
  } else {
    return '3'; // Summer semester
  }
}

export async function getDashboardOverview() {
  const year = getCurrentAcademicYear();
  const semester = getCurrentSemester();

  // Fetch all data in parallel
  const [classRes, absentRes, componentsRes] = await Promise.all([
    getClassesByYearSemester(year, semester),
    getAbsentSemester(year, semester),
    getGradeComponents({ year: parseInt(year, 10), semester: parseInt(semester, 10), perPage: -1 }),
  ]);

  const classes = classRes?.data?.data?.data || [];
  const absentData = absentRes?.data?.data?.data || [];
  const gradeComponents = componentsRes?.data?.data?.data || [];

  const totalStudents = classes.reduce((sum: number, cls: any) => sum + (cls.size || 0), 0);

  // Simplified attendance rate calculation
  const totalAbsences = absentData.reduce(
    (sum: number, item: any) => sum + (item.number_of_absence || 0),
    0
  );
  const totalPossibleAttendances = totalStudents * 15; // Assuming 15 sessions per semester
  const attendanceRate =
    totalPossibleAttendances > 0
      ? Math.round(((totalPossibleAttendances - totalAbsences) / totalPossibleAttendances) * 100)
      : 100;

  return {
    totalClasses: classes.length,
    totalStudents,
    attendanceRate,
    pendingGrades: gradeComponents.length, // Simplified
  };
}
