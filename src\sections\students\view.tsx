'use client';

import dayjs from 'dayjs';
import { useState, useEffect, useCallback } from 'react';

import Card from '@mui/material/Card';
import Chip from '@mui/material/Chip';
import Grid from '@mui/material/Grid2';
import Table from '@mui/material/Table';
import Stack from '@mui/material/Stack';
import Avatar from '@mui/material/Avatar';
import Select from '@mui/material/Select';
import Tooltip from '@mui/material/Tooltip';
import TableRow from '@mui/material/TableRow';
import MenuItem from '@mui/material/MenuItem';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import InputLabel from '@mui/material/InputLabel';
import FormControl from '@mui/material/FormControl';
import InputAdornment from '@mui/material/InputAdornment';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import axios from 'src/lib/axios';
import { DashboardContent } from 'src/layouts/dashboard';
import { fetchAllClassStudents } from 'src/actions/ttu-student-adapter';
import {
  fetchClassOptions,
  getCurrentSemester,
  getCurrentAcademicYear,
} from 'src/actions/ttu-report-adapter';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';
import { TablePaginationCustom } from 'src/components/table';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';

type Student = {
  id: string;
  studentId: string;
  name: string;
  email: string;
  phone?: string;
  avatar?: string;
  classId: string;
  className: string;
  year: number;
  semester: number;
  // Attendance info
  totalAbsences?: number;
  hasPermission?: boolean;
  attendanceStatus?: 'good' | 'warning' | 'critical';
};

type AbsentData = {
  _id: number;
  is_permission: number;
  number_of_absence: number;
  student: {
    _id: number;
    first_name: string;
    last_name: string;
    email: string;
    avatar?: string;
  };
  attendance: {
    _id: number;
    year: number;
    semester: number;
    code: string;
    date: string;
    class: number;
    content: string;
  };
};

type StudentFilters = {
  year: string;
  semester: string;
  classId: string;
  search: string;
};

export default function StudentsView() {
  const [students, setStudents] = useState<Student[]>([]);
  const [filteredStudents, setFilteredStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingAbsent, setLoadingAbsent] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const [filters, setFilters] = useState<StudentFilters>({
    year: getCurrentAcademicYear(),
    semester: getCurrentSemester(),
    classId: '',
    search: '',
  });

  const [classOptions, setClassOptions] = useState<Array<{ value: string; label: string }>>([]);
  const [absentData, setAbsentData] = useState<Map<string, AbsentData>>(new Map());

  // Fetch absent data
  const fetchAbsentData = useCallback(async (year: string, semester: string) => {
    try {
      setLoadingAbsent(true);
      const response = await axios.get('/student/absent/list-absence', {
        params: { year, semester },
      });

      const absentList: AbsentData[] = response.data?.data?.data || [];
      const absentMap = new Map<string, AbsentData>();

      absentList.forEach((item) => {
        const studentEmail = item.student.email;
        absentMap.set(studentEmail, item);
      });

      setAbsentData(absentMap);
    } catch (error) {
      console.error('Failed to fetch absent data:', error);
      // Set empty map on error so UI doesn't break
      setAbsentData(new Map());
    } finally {
      setLoadingAbsent(false);
    }
  }, []);

  // Load classes and absent data when year/semester changes
  useEffect(() => {
    const loadClasses = async () => {
      if (filters.year) {
        try {
          const opts = await fetchClassOptions(filters.year, filters.semester);
          setClassOptions(opts);

          // Also fetch absent data for the year/semester
          if (filters.semester) {
            await fetchAbsentData(filters.year, filters.semester);
          }
        } catch (error) {
          console.error('Failed to load classes:', error);
        }
      } else {
        setClassOptions([]);
        setAbsentData(new Map());
      }
    };
    loadClasses();
  }, [filters.year, filters.semester, fetchAbsentData]);

  // Load students
  const loadStudents = useCallback(async () => {
    if (!filters.year || !filters.semester) return;

    // Only set loading if we actually have a class to load
    if (!filters.classId) {
      setStudents([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);

      // Get students for specific class
      const classStudents = await fetchAllClassStudents(filters.classId);

      const selectedClass = classOptions.find((opt) => opt.value === filters.classId);
      const className = selectedClass?.label || 'Lớp học';
      const allStudents = classStudents.map((item: any) => {
        // For enrollment records, use student._id as the main ID
        const student = item.student || item;
        const studentRealId = student._id || item._id || item.id || '';
        const studentEmail = student.email || item.email || '';

        const absentInfo = absentData.get(studentEmail);
        const totalAbsences = absentInfo?.number_of_absence || 0;

        return {
          id: studentRealId,
          studentId: student.student_id || item.student_id || item.studentId || '',
          name:
            item.fullName ||
            `${student.first_name || item.first_name || ''} ${student.last_name || item.last_name || ''}`.trim() ||
            '',
          email: student.email || item.email || '',
          phone: '', // Will be loaded from student details if needed
          avatar: student.avatar || item.avatar,
          classId: filters.classId || '',
          className,
          year: Number(filters.year),
          semester: Number(filters.semester),
          totalAbsences,
          hasPermission: absentInfo?.is_permission === 1,
          attendanceStatus: (totalAbsences === 0
            ? 'good'
            : totalAbsences <= 2
              ? 'warning'
              : 'critical') as 'good' | 'warning' | 'critical',
        };
      });

      setStudents(allStudents);
    } catch (error) {
      console.error('Failed to load students:', error);
      toast.error('Có lỗi xảy ra khi tải danh sách sinh viên');
    } finally {
      setLoading(false);
    }
  }, [filters.year, filters.semester, filters.classId, classOptions, absentData]);

  useEffect(() => {
    // Only load students after classOptions are loaded
    if (classOptions.length > 0 || filters.classId) {
      loadStudents();
    }
  }, [loadStudents, classOptions.length, filters.classId]);

  // Filter by search
  useEffect(() => {
    if (!filters.search) {
      setFilteredStudents(students);
    } else {
      const filtered = students.filter(
        (student) =>
          student.name.toLowerCase().includes(filters.search.toLowerCase()) ||
          student.email.toLowerCase().includes(filters.search.toLowerCase()) ||
          student.studentId.toLowerCase().includes(filters.search.toLowerCase())
      );
      setFilteredStudents(filtered);
    }
  }, [students, filters.search]);

  const handleFilterChange = (field: keyof StudentFilters, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [field]: value,
      // Reset dependent fields
      ...(field === 'year' && { classId: '' }),
      ...(field === 'semester' && { classId: '' }),
    }));
    setPage(0); // Reset pagination
  };

  const handleYearChange = (val: dayjs.Dayjs | null) => {
    const year = val ? String(val.year()) : '';
    handleFilterChange('year', year);
  };

  const handlePageChange = (_event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleRowsPerPageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Paginated data
  const paginatedStudents = filteredStudents.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="Sinh viên"
        links={[{ name: 'Trang chủ', href: paths.dashboard.root }, { name: 'Sinh viên' }]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card>
        {/* Filters */}
        <Stack spacing={2} sx={{ p: 3 }}>
          <Typography variant="h6">Bộ lọc sinh viên</Typography>

          <Grid container spacing={3}>
            <Grid size={{ xs: 12, md: 2.4 }}>
              <FormControl fullWidth>
                <DatePicker
                  views={['year']}
                  label="Năm học"
                  value={filters.year ? dayjs(`${filters.year}-01-01`) : null}
                  onChange={handleYearChange}
                />
              </FormControl>
            </Grid>

            <Grid size={{ xs: 12, md: 2.4 }}>
              <FormControl fullWidth>
                <InputLabel>Học kỳ</InputLabel>
                <Select
                  value={filters.semester}
                  onChange={(e) => handleFilterChange('semester', e.target.value)}
                  label="Học kỳ"
                >
                  <MenuItem value="1">Học kỳ 1 (Fall)</MenuItem>
                  <MenuItem value="2">Học kỳ 2 (Spring)</MenuItem>
                  <MenuItem value="3">Học kỳ 3 (Summer)</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid size={{ xs: 12, md: 2.4 }}>
              <FormControl fullWidth>
                <InputLabel>Lớp học</InputLabel>
                <Select
                  value={filters.classId}
                  onChange={(e) => handleFilterChange('classId', e.target.value)}
                  label="Lớp học"
                  disabled={!filters.year}
                >
                  <MenuItem value="">Tất cả lớp</MenuItem>
                  {classOptions.map((opt) => (
                    <MenuItem key={opt.value} value={opt.value}>
                      {opt.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid size={{ xs: 12, md: 4.8 }}>
              <TextField
                fullWidth
                placeholder="Tìm kiếm sinh viên..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                slotProps={{
                  input: {
                    startAdornment: (
                      <InputAdornment position="start">
                        <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />
                      </InputAdornment>
                    ),
                  },
                }}
              />
            </Grid>
          </Grid>
        </Stack>

        {/* Table */}
        <Scrollbar>
          <Table size="small" sx={{ minWidth: 800 }}>
            <TableHead>
              <TableRow>
                <TableCell>Sinh viên</TableCell>
                <TableCell>Mã sinh viên</TableCell>
                <TableCell>Lớp học</TableCell>
                <TableCell>Điểm danh</TableCell>
                <TableCell>Số điện thoại</TableCell>
                <TableCell align="right">Hành động</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    <Typography variant="body2" color="text.secondary">
                      {loadingAbsent
                        ? 'Đang tải dữ liệu điểm danh...'
                        : 'Đang tải danh sách sinh viên...'}
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : paginatedStudents.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    <Typography variant="body2" color="text.secondary">
                      {!filters.year || !filters.semester
                        ? 'Vui lòng chọn năm học và học kỳ để xem danh sách sinh viên'
                        : classOptions.length === 0
                          ? 'Không có lớp học nào trong năm học và học kỳ này'
                          : !filters.classId
                            ? 'Vui lòng chọn lớp học cụ thể để xem danh sách sinh viên'
                            : 'Không có sinh viên nào'}
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                paginatedStudents.map((student) => (
                  <TableRow key={student.id} hover>
                    <TableCell>
                      <Stack direction="row" spacing={2} alignItems="center">
                        <Avatar
                          src={student.avatar}
                          alt={student.name}
                          sx={{ width: 40, height: 40 }}
                        >
                          {student.name.charAt(0)}
                        </Avatar>
                        <div>
                          <Typography
                            variant="subtitle2"
                            component={RouterLink}
                            href={`${paths.dashboard.studentReports}/${student.id}?year=${student.year}&semester=${student.semester}`}
                            sx={{
                              textDecoration: 'none',
                              color: 'primary.main',
                              '&:hover': { textDecoration: 'underline' },
                            }}
                          >
                            {student.name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {student.email}
                          </Typography>
                        </div>
                      </Stack>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">{student.studentId}</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">{student.className}</Typography>
                    </TableCell>
                    <TableCell>
                      <Stack direction="row" spacing={1} alignItems="center">
                        <Chip
                          size="small"
                          label={`${student.totalAbsences || 0} vắng`}
                          color={
                            student.attendanceStatus === 'good'
                              ? 'success'
                              : student.attendanceStatus === 'warning'
                                ? 'warning'
                                : 'error'
                          }
                          variant="outlined"
                        />
                        {student.hasPermission && (
                          <Chip size="small" label="Có phép" color="info" variant="filled" />
                        )}
                      </Stack>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">{student.phone || 'Chưa có'}</Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Tooltip title="Xem thông tin chi tiết">
                        <IconButton
                          component={RouterLink}
                          href={`${paths.dashboard.studentReports}/${student.id}?year=${student.year}&semester=${student.semester}`}
                          color="primary"
                          size="small"
                        >
                          <Iconify icon="solar:user-bold" />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </Scrollbar>

        <TablePaginationCustom
          count={filteredStudents.length}
          page={page}
          rowsPerPage={rowsPerPage}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleRowsPerPageChange}
          dense
        />
      </Card>
    </DashboardContent>
  );
}
