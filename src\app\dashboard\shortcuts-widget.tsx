'use client';

import Card from '@mui/material/Card';
import { alpha } from '@mui/material/styles';
import Typography from '@mui/material/Typography';
import CardActionArea from '@mui/material/CardActionArea';
import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

const SHORTCUT_GROUPS = [
  {
    groupTitle: 'Quản lý học tập',
    items: [
      {
        title: 'Quản lý học kỳ',
        path: paths.dashboard.semesters,
        icon: 'solar:calendar-bold-duotone',
      },
      {
        title: '<PERSON><PERSON> s<PERSON>ch lớp',
        path: paths.dashboard.classes.root,
        icon: 'solar:course-up-bold-duotone',
      },
      {
        title: 'Sinh viên',
        path: paths.dashboard.students,
        icon: 'solar:users-group-rounded-bold-duotone',
      },
      {
        title: 'Sổ điểm lớp',
        path: paths.dashboard.gradebook,
        icon: 'solar:notebook-bold-duotone',
      },
      {
        title: 'Nhập điểm',
        path: paths.dashboard.gradeEntry,
        icon: 'solar:pen-new-square-bold-duotone',
      },
    ],
  },
  {
    groupTitle: 'Quản lý điểm danh',
    items: [
      {
        title: 'Tạo phiên điểm danh',
        path: paths.dashboard.attendance,
        icon: 'solar:user-plus-rounded-bold-duotone',
      },
      {
        title: 'Lịch sử điểm danh',
        path: paths.dashboard.attendanceHistory,
        icon: 'solar:history-bold-duotone',
      },
    ],
  },
  {
    groupTitle: 'Báo cáo & Thống kê',
    items: [
      {
        title: 'Báo cáo điểm danh',
        path: paths.dashboard.reports,
        icon: 'solar:file-text-bold-duotone',
      },
      {
        title: 'Báo cáo sinh viên',
        path: paths.dashboard.studentReports,
        icon: 'solar:user-id-bold-duotone',
      },
    ],
  },
];

// ----------------------------------------------------------------------

export function ShortcutsWidget() {
  return (
    <Stack spacing={5}>
      {SHORTCUT_GROUPS.map((group) => (
        <Box key={group.groupTitle}>
          <Typography variant="h6" sx={{ mb: 3 }}>
            {group.groupTitle}
          </Typography>
          <Box
            sx={{
              display: 'grid',
              gap: 3,
              gridTemplateColumns: {
                xs: 'repeat(1, 1fr)',
                sm: 'repeat(2, 1fr)',
                md: 'repeat(3, 1fr)',
                lg: 'repeat(4, 1fr)',
              },
            }}
          >
            {group.items.map((shortcut) => (
              <Card
                key={shortcut.title}
                sx={{
                  transition: (theme) => theme.transitions.create('transform'),
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: (theme) => theme.customShadows.z16,
                  },
                }}
              >
                <CardActionArea
                  component={RouterLink}
                  href={shortcut.path}
                  sx={{
                    p: 3,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'flex-start',
                    textAlign: 'left',
                  }}
                >
                  <Iconify icon={shortcut.icon} width={48} sx={{ mr: 2, color: 'primary.main' }} />
                  <Box>
                    <Typography variant="subtitle1">{shortcut.title}</Typography>
                  </Box>
                </CardActionArea>
              </Card>
            ))}
          </Box>
        </Box>
      ))}
    </Stack>
  );
}
