import SvgIcon from '@mui/material/SvgIcon';

import { Iconify } from 'src/components/iconify';

import type { AccountDrawerProps } from './components/account-drawer';

// ----------------------------------------------------------------------

export const _account: AccountDrawerProps['data'] = [
  { label: 'Trang chủ', href: '/', icon: <Iconify icon="solar:home-angle-bold-duotone" /> },
  {
    label: '<PERSON><PERSON> sơ cá nhân',
    href: '/dashboard/user/profile',
    icon: <Iconify icon="solar:user-bold-duotone" />,
  },
  {
    label: 'Danh sách lớp',
    href: '/dashboard/classes',
    icon: <Iconify icon="solar:book-2-bold-duotone" />,
  },
  {
    label: '<PERSON>iể<PERSON> danh',
    href: '/dashboard/attendance',
    icon: <Iconify icon="solar:calendar-mark-bold-duotone" />,
  },
  {
    label: '<PERSON><PERSON> điểm lớp',
    href: '/dashboard/gradebook',
    icon: <Iconify icon="solar:calculator-bold-duotone" />,
  },
  {
    label: 'Nhập điểm',
    href: '/dashboard/grade-entry',
    icon: <Iconify icon="solar:pen-new-square-bold-duotone" />,
  },
  {
    label: 'Báo cáo',
    href: '/dashboard/reports',
    icon: <Iconify icon="solar:chart-bold-duotone" />,
  },
  {
    label: 'Cài đặt',
    href: '/dashboard/settings',
    icon: <Iconify icon="solar:settings-bold-duotone" />,
  },
];
