'use client';

import dayjs from 'dayjs';
import { useState, useEffect, useCallback } from 'react';

import Card from '@mui/material/Card';
import Chip from '@mui/material/Chip';
import Grid from '@mui/material/Grid2';
import Table from '@mui/material/Table';
import Stack from '@mui/material/Stack';
import Avatar from '@mui/material/Avatar';
import Select from '@mui/material/Select';
import Tooltip from '@mui/material/Tooltip';
import TableRow from '@mui/material/TableRow';
import MenuItem from '@mui/material/MenuItem';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import InputLabel from '@mui/material/InputLabel';
import FormControl from '@mui/material/FormControl';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { DashboardContent } from 'src/layouts/dashboard';
import { getAbsentClassReport, getAbsentSemesterReport } from 'src/actions/ttu-api';
import {
  fetchClassOptions,
  getCurrentSemester,
  getCurrentAcademicYear,
} from 'src/actions/ttu-report-adapter';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';
import { TablePaginationCustom } from 'src/components/table';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';

type StudentAbsentReport = {
  id: string;
  studentId: string;
  studentName: string;
  studentEmail: string;
  avatar?: string;
  numberOfAbsence: number;
  classId: string;
  className: string;
  year: number;
  semester: number;
};

type StudentReportFilters = {
  year: string;
  semester: string;
  classId: string;
  studentId: string;
};

export default function StudentReportsView() {
  const [reports, setReports] = useState<StudentAbsentReport[]>([]);
  const [filteredReports, setFilteredReports] = useState<StudentAbsentReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const [filters, setFilters] = useState<StudentReportFilters>({
    year: getCurrentAcademicYear(),
    semester: getCurrentSemester(),
    classId: '',
    studentId: 'all',
  });

  const [classOptions, setClassOptions] = useState<Array<{ value: string; label: string }>>([]);

  // Load classes when year/semester changes
  useEffect(() => {
    const loadClasses = async () => {
      if (filters.year) {
        try {
          const opts = await fetchClassOptions(filters.year, filters.semester);
          setClassOptions(opts);
        } catch (error) {
          console.error('Failed to load classes:', error);
        }
      } else {
        setClassOptions([]);
      }
    };
    loadClasses();
  }, [filters.year, filters.semester]);

  // Load absent reports
  const loadReports = useCallback(async () => {
    if (!filters.year || !filters.semester) return;

    try {
      setLoading(true);
      let result;

      if (filters.classId) {
        // Get reports for specific class
        result = await getAbsentClassReport(filters.year, filters.semester, filters.classId);
      } else {
        // Get reports for entire semester
        result = await getAbsentSemesterReport(filters.year, filters.semester);
      }

      const data = result?.data?.data?.data || [];

      const mappedReports: StudentAbsentReport[] = data.map((item: any) => ({
        id: item._id?.toString() || '',
        studentId: item.student?._id?.toString() || '',
        studentName: `${item.student?.last_name || ''} ${item.student?.first_name || ''}`.trim(),
        studentEmail: item.student?.email || '',
        avatar: item.student?.avatar
          ? `https://intranet.ttu.edu.vn/uploads/${item.student.avatar}`
          : undefined,
        numberOfAbsence: item.number_of_absence || 0,
        classId: item.attendance?.class?.toString() || filters.classId,
        className: item.attendance?.code || 'Lớp học',
        year: item.attendance?.year || Number(filters.year),
        semester: item.attendance?.semester || Number(filters.semester),
      }));

      setReports(mappedReports);
      setTotal(mappedReports.length);
    } catch (error) {
      console.error('Failed to load student reports:', error);
      toast.error('Có lỗi xảy ra khi tải báo cáo sinh viên');
    } finally {
      setLoading(false);
    }
  }, [filters.year, filters.semester, filters.classId]);

  useEffect(() => {
    loadReports();
  }, [loadReports]);

  // Filter by student
  useEffect(() => {
    if (filters.studentId === 'all') {
      setFilteredReports(reports);
    } else {
      const filtered = reports.filter((report) => report.studentId === filters.studentId);
      setFilteredReports(filtered);
    }
  }, [reports, filters.studentId]);

  const handleFilterChange = (field: keyof StudentReportFilters, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [field]: value,
      // Reset dependent fields
      ...(field === 'year' && { classId: '', studentId: 'all' }),
      ...(field === 'semester' && { classId: '', studentId: 'all' }),
      ...(field === 'classId' && { studentId: 'all' }),
    }));
    setPage(0); // Reset pagination
  };

  const handleYearChange = (val: dayjs.Dayjs | null) => {
    const year = val ? String(val.year()) : '';
    handleFilterChange('year', year);
  };

  const handlePageChange = (_event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleRowsPerPageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const getAbsenceColor = (count: number) => {
    if (count === 0) return 'success';
    if (count <= 3) return 'warning';
    return 'error';
  };

  const getAbsenceLabel = (count: number) => {
    if (count === 0) return 'Không vắng';
    return `${count} buổi vắng`;
  };

  // Paginated data
  const paginatedReports = filteredReports.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="Báo cáo sinh viên"
        links={[{ name: 'Trang chủ', href: paths.dashboard.root }, { name: 'Báo cáo sinh viên' }]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card>
        {/* Filters */}
        <Stack spacing={2} sx={{ p: 3 }}>
          <Typography variant="h6">Bộ lọc báo cáo</Typography>

          <Grid container spacing={3}>
            <Grid size={{ xs: 12, md: 3 }}>
              <FormControl fullWidth>
                <DatePicker
                  views={['year']}
                  label="Năm học"
                  value={filters.year ? dayjs(`${filters.year}-01-01`) : null}
                  onChange={handleYearChange}
                />
              </FormControl>
            </Grid>

            <Grid size={{ xs: 12, md: 3 }}>
              <FormControl fullWidth>
                <InputLabel>Học kỳ</InputLabel>
                <Select
                  value={filters.semester}
                  onChange={(e) => handleFilterChange('semester', e.target.value)}
                  label="Học kỳ"
                >
                  <MenuItem value="1">Học kỳ 1 (Fall)</MenuItem>
                  <MenuItem value="2">Học kỳ 2 (Spring)</MenuItem>
                  <MenuItem value="3">Học kỳ 3 (Summer)</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid size={{ xs: 12, md: 3 }}>
              <FormControl fullWidth>
                <InputLabel>Lớp học</InputLabel>
                <Select
                  value={filters.classId}
                  onChange={(e) => handleFilterChange('classId', e.target.value)}
                  label="Lớp học"
                  disabled={!filters.year}
                >
                  <MenuItem value="">Tất cả lớp</MenuItem>
                  {classOptions.map((opt) => (
                    <MenuItem key={opt.value} value={opt.value}>
                      {opt.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid size={{ xs: 12, md: 3 }}>
              <FormControl fullWidth>
                <InputLabel>Sinh viên</InputLabel>
                <Select
                  value={filters.studentId}
                  onChange={(e) => handleFilterChange('studentId', e.target.value)}
                  label="Sinh viên"
                >
                  <MenuItem value="all">Tất cả sinh viên</MenuItem>
                  {reports.map((report) => (
                    <MenuItem key={report.studentId} value={report.studentId}>
                      {report.studentName}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Stack>

        {/* Table */}
        <Scrollbar>
          <Table size="small" sx={{ minWidth: 800 }}>
            <TableHead>
              <TableRow>
                <TableCell>Sinh viên</TableCell>
                <TableCell>Lớp học</TableCell>
                <TableCell>Số buổi vắng</TableCell>
                <TableCell align="right">Hành động</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={4} align="center">
                    <Typography variant="body2" color="text.secondary">
                      Đang tải...
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : paginatedReports.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} align="center">
                    <Typography variant="body2" color="text.secondary">
                      Không có dữ liệu báo cáo
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                paginatedReports.map((report) => (
                  <TableRow key={`${report.studentId}-${report.classId}`} hover>
                    <TableCell>
                      <Stack direction="row" spacing={2} alignItems="center">
                        <Avatar
                          src={report.avatar}
                          alt={report.studentName}
                          sx={{ width: 40, height: 40 }}
                        >
                          {report.studentName.charAt(0)}
                        </Avatar>
                        <div>
                          <Typography variant="subtitle2">{report.studentName}</Typography>
                          <Typography variant="body2" color="text.secondary">
                            {report.studentEmail}
                          </Typography>
                        </div>
                      </Stack>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">{report.className}</Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={getAbsenceLabel(report.numberOfAbsence)}
                        color={getAbsenceColor(report.numberOfAbsence) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="right">
                      <Tooltip title="Xem báo cáo chi tiết">
                        <IconButton
                          component={RouterLink}
                          href={`${paths.dashboard.studentReports}/${report.studentId}?year=${report.year}&semester=${report.semester}${filters.classId ? `&classId=${report.classId}` : ''}`}
                          color="primary"
                          size="small"
                        >
                          <Iconify icon="solar:document-text-bold" />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </Scrollbar>

        <TablePaginationCustom
          count={filteredReports.length}
          page={page}
          rowsPerPage={rowsPerPage}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleRowsPerPageChange}
          dense
        />
      </Card>
    </DashboardContent>
  );
}
