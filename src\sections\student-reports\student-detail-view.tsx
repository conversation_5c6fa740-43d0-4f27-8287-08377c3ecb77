'use client';

import { useSearchParams } from 'next/navigation';
import { useState, useEffect, useCallback } from 'react';

import Card from '@mui/material/Card';
import Chip from '@mui/material/Chip';
import Grid from '@mui/material/Grid2';
import Table from '@mui/material/Table';
import Stack from '@mui/material/Stack';
import Paper from '@mui/material/Paper';
import Avatar from '@mui/material/Avatar';
import TableRow from '@mui/material/TableRow';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import Typography from '@mui/material/Typography';

import { paths } from 'src/routes/paths';

import { DashboardContent } from 'src/layouts/dashboard';
import {
  getStudentDetails,
  getAbsentStudentReport,
  getAbsentStudentByYearSemester,
} from 'src/actions/ttu-api';

import { toast } from 'src/components/snackbar';
import { Scrollbar } from 'src/components/scrollbar';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';

type StudentAbsentDetail = {
  id: string;
  attendanceId: string;
  attendanceDate: string;
  attendanceContent: string;
  className: string;
  isPermission: boolean;
  createdAt: string;
};

type ClassAbsentSummary = {
  classId: string;
  className: string;
  classCode: string;
  totalAbsence: number;
  permissionCount: number;
  unauthorizedCount: number;
  details: StudentAbsentDetail[];
};

type StudentInfo = {
  id: string;
  name: string;
  email: string;
  phone?: string;
  avatar?: string;
  totalAbsence: number;
  permissionCount: number;
  unauthorizedCount: number;
};

type Props = {
  studentId: string;
};

export default function StudentDetailReportView({ studentId }: Props) {
  const searchParams = useSearchParams();
  const year = searchParams.get('year') || '';
  const semester = searchParams.get('semester') || '';
  const classId = searchParams.get('classId') || '';

  const [studentInfo, setStudentInfo] = useState<StudentInfo | null>(null);
  const [absentDetails, setAbsentDetails] = useState<StudentAbsentDetail[]>([]);
  const [classSummaries, setClassSummaries] = useState<ClassAbsentSummary[]>([]);
  const [loading, setLoading] = useState(true);

  const loadStudentReport = useCallback(async () => {
    try {
      setLoading(true);

      // Load student details first to get complete info including phone
      let studentDetailResult;
      try {
        studentDetailResult = await getStudentDetails(studentId);
        console.log('studentDetailResult', studentDetailResult);
      } catch {
        console.log('Could not load student details, will use data from absent records');
      }

      let result;
      if (classId) {
        // Load specific class data
        result = await getAbsentStudentReport(studentId, classId);
      } else {
        // Load all classes data for year/semester
        result = await getAbsentStudentByYearSemester(year, semester, studentId);
      }

      const data = result?.data?.data?.data || [];

      // Set student info even if no absent data
      if (data.length > 0 || studentDetailResult) {
        // Extract student info from first record or from student details API
        const firstRecord = data.length > 0 ? data[0] : null;

        const studentFromAbsent = firstRecord?.student || {};
        const studentFromDetails =
          studentDetailResult?.data?.data || studentDetailResult?.data || {};

        // Merge student info, prioritizing details API
        const student = { ...studentFromAbsent, ...studentFromDetails };

        const permissionCount = data.filter((item: any) => item.is_permission).length;
        const unauthorizedCount = data.length - permissionCount;

        setStudentInfo({
          id: student._id?.toString() || studentId,
          name: `${student.last_name || ''} ${student.first_name || ''}`.trim(),
          email: student.email || '',
          phone: student.phone || student.phone_number || student.mobile || student.tel || '',
          avatar: student.avatar
            ? `https://intranet.ttu.edu.vn/uploads/${student.avatar}`
            : undefined,
          totalAbsence: data.length,
          permissionCount,
          unauthorizedCount,
        });

        // Group by class if loading all classes
        if (!classId) {
          const classGroups = data.reduce((acc: any, item: any) => {
            const classKey = item.attendance?.class?.toString() || 'unknown';
            if (!acc[classKey]) {
              acc[classKey] = [];
            }
            acc[classKey].push(item);
            return acc;
          }, {});

          const summaries: ClassAbsentSummary[] = Object.entries(classGroups).map(
            ([classKey, items]: [string, any]) => {
              const firstItem = items[0];
              const permCount = items.filter((item: any) => item.is_permission).length;

              return {
                classId: classKey,
                className: firstItem.attendance?.code || 'Lớp học',
                classCode: firstItem.attendance?.code || '',
                totalAbsence: items.length,
                permissionCount: permCount,
                unauthorizedCount: items.length - permCount,
                details: items.map((item: any) => ({
                  id: item._id?.toString() || '',
                  attendanceId: item.attendance?._id?.toString() || '',
                  attendanceDate: item.attendance?.date || '',
                  attendanceContent: item.attendance?.content || 'Điểm danh',
                  className: item.attendance?.code || 'Lớp học',
                  isPermission: Boolean(item.is_permission),
                  createdAt: item.created_at || '',
                })),
              };
            }
          );

          setClassSummaries(summaries);
        } else {
          // Map absent details for single class
          const mappedDetails: StudentAbsentDetail[] = data.map((item: any) => ({
            id: item._id?.toString() || '',
            attendanceId: item.attendance?._id?.toString() || '',
            attendanceDate: item.attendance?.date || '',
            attendanceContent: item.attendance?.content || 'Điểm danh',
            className: item.attendance?.code || 'Lớp học',
            isPermission: Boolean(item.is_permission),
            createdAt: item.created_at || '',
          }));

          setAbsentDetails(mappedDetails);
        }
      }
    } catch (error) {
      console.error('Failed to load student report:', error);
      toast.error('Có lỗi xảy ra khi tải báo cáo sinh viên');
    } finally {
      setLoading(false);
    }
  }, [studentId, classId, year, semester]);

  useEffect(() => {
    if (studentId && (classId || (year && semester))) {
      loadStudentReport();
    }
  }, [loadStudentReport, studentId, classId, year, semester]);

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('vi-VN');
    } catch {
      return dateString;
    }
  };

  const getAbsenceTypeColor = (isPermission: boolean) => (isPermission ? 'warning' : 'error');

  const getAbsenceTypeLabel = (isPermission: boolean) => (isPermission ? 'Có phép' : 'Không phép');

  if (loading) {
    return (
      <DashboardContent>
        <Typography>Đang tải...</Typography>
      </DashboardContent>
    );
  }

  if (!studentInfo) {
    return (
      <DashboardContent>
        <Typography>Không tìm thấy thông tin sinh viên</Typography>
      </DashboardContent>
    );
  }

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading="Thông tin chi tiết sinh viên"
        links={[
          { name: 'Trang chủ', href: paths.dashboard.root },
          { name: 'Sinh viên', href: paths.dashboard.students },
          { name: studentInfo.name },
        ]}
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Grid container spacing={3}>
        {/* Student Info Card */}
        <Grid size={{ xs: 12, md: 4 }}>
          <Card sx={{ p: 3 }}>
            <Stack spacing={2} alignItems="center">
              <Avatar
                src={studentInfo.avatar}
                alt={studentInfo.name}
                sx={{ width: 80, height: 80 }}
              >
                {studentInfo.name.charAt(0)}
              </Avatar>

              <div style={{ textAlign: 'center' }}>
                <Typography variant="h6">{studentInfo.name}</Typography>
                <Typography variant="body2" color="text.secondary">
                  {studentInfo.email}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  📞 {studentInfo.phone || 'Chưa có số điện thoại'}
                </Typography>
              </div>

              <Stack spacing={1} sx={{ width: '100%' }}>
                <Paper variant="outlined" sx={{ p: 2, textAlign: 'center' }}>
                  <Typography variant="h4" color="error">
                    {studentInfo.totalAbsence}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Tổng số buổi vắng
                  </Typography>
                </Paper>

                <Grid container spacing={1}>
                  <Grid size={6}>
                    <Paper variant="outlined" sx={{ p: 1.5, textAlign: 'center' }}>
                      <Typography variant="h6" color="warning.main">
                        {studentInfo.permissionCount}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Có phép
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid size={6}>
                    <Paper variant="outlined" sx={{ p: 1.5, textAlign: 'center' }}>
                      <Typography variant="h6" color="error.main">
                        {studentInfo.unauthorizedCount}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Không phép
                      </Typography>
                    </Paper>
                  </Grid>
                </Grid>
              </Stack>
            </Stack>
          </Card>
        </Grid>

        {/* Absent Details */}
        <Grid size={{ xs: 12, md: 8 }}>
          {classId ? (
            // Single class view
            <Card>
              <Stack
                direction="row"
                alignItems="center"
                justifyContent="space-between"
                sx={{ p: 3 }}
              >
                <Typography variant="h6">Chi tiết các buổi vắng</Typography>
                <Typography variant="body2" color="text.secondary">
                  Năm {year} - Học kỳ {semester}
                </Typography>
              </Stack>

              <Scrollbar>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Ngày</TableCell>
                      <TableCell>Nội dung</TableCell>
                      <TableCell>Loại vắng</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {absentDetails.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={3} align="center">
                          <Typography variant="body2" color="text.secondary">
                            Không có dữ liệu vắng mặt
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ) : (
                      absentDetails.map((detail) => (
                        <TableRow key={detail.id} hover>
                          <TableCell>
                            <Typography variant="body2">
                              {formatDate(detail.attendanceDate)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">{detail.attendanceContent}</Typography>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={getAbsenceTypeLabel(detail.isPermission)}
                              color={getAbsenceTypeColor(detail.isPermission) as any}
                              size="small"
                            />
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </Scrollbar>
            </Card>
          ) : (
            // Multiple classes view
            <Stack spacing={3}>
              <Typography variant="h6">
                Báo cáo theo từng lớp - Năm {year} - Học kỳ {semester}
              </Typography>

              {classSummaries.length === 0 ? (
                <Card sx={{ p: 3 }}>
                  <Typography variant="body2" color="text.secondary" align="center">
                    Không có dữ liệu vắng mặt
                  </Typography>
                </Card>
              ) : (
                classSummaries.map((classSummary) => (
                  <Card key={classSummary.classId}>
                    <Stack
                      direction="row"
                      alignItems="center"
                      justifyContent="space-between"
                      sx={{ p: 3 }}
                    >
                      <Typography variant="h6">{classSummary.className}</Typography>
                      <Stack direction="row" spacing={2}>
                        <Chip
                          label={`${classSummary.totalAbsence} buổi vắng`}
                          color="error"
                          size="small"
                        />
                        <Chip
                          label={`${classSummary.permissionCount} có phép`}
                          color="warning"
                          size="small"
                        />
                        <Chip
                          label={`${classSummary.unauthorizedCount} không phép`}
                          color="error"
                          size="small"
                        />
                      </Stack>
                    </Stack>

                    <Scrollbar>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Ngày</TableCell>
                            <TableCell>Nội dung</TableCell>
                            <TableCell>Loại vắng</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {classSummary.details.map((detail) => (
                            <TableRow key={detail.id} hover>
                              <TableCell>
                                <Typography variant="body2">
                                  {formatDate(detail.attendanceDate)}
                                </Typography>
                              </TableCell>
                              <TableCell>
                                <Typography variant="body2">{detail.attendanceContent}</Typography>
                              </TableCell>
                              <TableCell>
                                <Chip
                                  label={getAbsenceTypeLabel(detail.isPermission)}
                                  color={getAbsenceTypeColor(detail.isPermission) as any}
                                  size="small"
                                />
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </Scrollbar>
                  </Card>
                ))
              )}
            </Stack>
          )}
        </Grid>
      </Grid>
    </DashboardContent>
  );
}
