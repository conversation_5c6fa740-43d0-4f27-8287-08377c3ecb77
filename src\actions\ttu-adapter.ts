import type { ClassWithStats } from 'src/types/academic';
import type { AxiosResponse } from 'axios';

import axios from 'src/lib/axios';

// Map TTU API class response to UI ClassWithStats
// TTU endpoints we have: getClass(year), getDataClass(year, semester)
// Assume API returns { data: [...] } with fields similar to: { id/_id, code, name, term, size }

export async function fetchClassesByYear(year: string | number): Promise<ClassWithStats[]> {
  const res: AxiosResponse<any> = await axios.get(`student/class`, {
    params: { year, perPage: -1 },
  });
  // API shape: { code, message, data: { total, data: [...] } }
  const items = res?.data?.data?.data ?? res?.data?.data ?? [];
  return items.map(mapTtuClassToUi);
}

export async function fetchClassesByYearSemester(
  year: string | number,
  semester: string | number
): Promise<ClassWithStats[]> {
  const res: AxiosResponse<any> = await axios.get(`student/class`, {
    params: { year, semester, perPage: -1 },
  });
  // API shape: { code, message, data: { total, data: [...] } }
  const items = res?.data?.data?.data ?? res?.data?.data ?? [];
  return items.map(mapTtuClassToUi);
}

export function mapTtuClassToUi(raw: any): ClassWithStats {
  const id = `${raw._id ?? raw.id ?? raw.class_id ?? raw.code}`;
  const sem = String(raw.semester ?? '');
  const semLabel = sem === '1' ? 'Fall' : sem === '2' ? 'Spring' : sem === '3' ? 'Summer' : sem;
  return {
    id,
    code: String(raw.code ?? raw.class_code ?? id),
    name: String(raw.name_vn ?? raw.class_name ?? raw.name ?? 'Lớp học'),
    nameEn: raw.name ?? undefined,
    term: `${raw.year ?? ''}${semLabel ? ` - ${semLabel}` : ''}`.trim(),
    size: Number(raw.size ?? raw.total_students ?? raw.students_count ?? 0),
    totalStudents: Number(raw.size ?? raw.total_students ?? raw.students_count ?? 0),
    nextSessionAt: raw.nextSessionAt ?? raw.next_session_at ?? undefined,
    status: 'active',
  };
}
