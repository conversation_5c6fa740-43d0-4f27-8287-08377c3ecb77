'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

import { Box } from '@mui/material';

import { useKeycloak } from 'src/auth/context/keycloak';

// ----------------------------------------------------------------------

export default function KeycloakLogoutCallbackPage() {
  const router = useRouter();
  const { signOutCallback } = useKeycloak();

  useEffect(() => {
    const handleLogoutCallback = async () => {
      try {
        await signOutCallback();
        console.log('Keycloak logout callback successful');

        // Clear any remaining auth data
        localStorage.removeItem('currentUser');
        localStorage.removeItem('accessToken');
        localStorage.removeItem('user');
        localStorage.removeItem('token');
        localStorage.removeItem('jwt_access_token');
        sessionStorage.clear();

        // Redirect to login page after successful logout
        router.replace('/auth/login');
      } catch (error) {
        console.error('Keycloak logout callback error:', error);
        // Still redirect to login page even if there's an error
        router.replace('/auth/login');
      }
    };

    handleLogoutCallback();
  }, [signOutCallback, router]);

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        flexDirection: 'column',
      }}
    >
      <Box>Đang đăng xuất...</Box>
      <Box sx={{ marginTop: '20px' }}>
        <Box
          sx={{
            border: '4px solid #f3f3f3',
            borderTop: '4px solid #3498db',
            borderRadius: '50%',
            width: '40px',
            height: '40px',
            animation: 'spin 2s linear infinite',
            '@keyframes spin': {
              '0%': { transform: 'rotate(0deg)' },
              '100%': { transform: 'rotate(360deg)' },
            },
          }}
        />
      </Box>
    </Box>
  );
}
