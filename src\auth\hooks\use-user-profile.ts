import { useState, useEffect } from 'react';

import { useAuthContext } from './use-auth-context';

// ----------------------------------------------------------------------

export interface UserProfile {
  id: string;
  displayName: string;
  email: string;
  photoURL?: string;
  phoneNumber?: string;
  role: string;
  firstName?: string;
  lastName?: string;
  department?: string;
  userType?: string;
  isActive?: boolean;
  isLecturer?: boolean;
  dob?: string;
  sex?: string;
}

export interface CurrentUserData {
  email: string;
  user_type: string;
  is_active: number;
  employee: {
    _id: number;
    is_lecturer: number;
    first_name: string;
    last_name: string;
    dob: string;
    sex: string;
    department: number;
  };
}

export function useUserProfile() {
  const { authenticated } = useAuthContext();
  const [user, setUser] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchUserProfile = () => {
    try {
      setLoading(true);
      setError(null);

      // Get user data from localStorage
      const currentUserStr =
        typeof window !== 'undefined' ? localStorage.getItem('currentUser') : null;

      if (!currentUserStr) {
        setError('No user data found in localStorage');
        setUser(null);
        return;
      }

      const currentUserData: CurrentUserData = JSON.parse(currentUserStr);

      // Transform localStorage data to match our UserProfile interface
      const userProfile: UserProfile = {
        id: currentUserData.employee._id.toString(),
        displayName: `${currentUserData.employee.first_name}`.trim(),
        email: currentUserData.email,
        photoURL: undefined, // No photo URL in the data
        phoneNumber: undefined, // No phone number in the data
        role: currentUserData.employee.is_lecturer ? 'lecturer' : 'employee',
        firstName: currentUserData.employee.first_name,
        lastName: currentUserData.employee.last_name,
        department: currentUserData.employee.department.toString(),
        userType: currentUserData.user_type,
        isActive: currentUserData.is_active === 1,
        isLecturer: currentUserData.employee.is_lecturer === 1,
        dob: currentUserData.employee.dob,
        sex: currentUserData.employee.sex,
      };

      setUser(userProfile);
    } catch (err) {
      console.error('Error parsing user profile from localStorage:', err);
      setError('Failed to parse user profile data');
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (authenticated) {
      fetchUserProfile();
    } else {
      setUser(null);
    }
  }, [authenticated]);

  return {
    user,
    loading,
    error,
    refetch: fetchUserProfile,
  };
}
