'use client';

import { User, UserManager, WebStorageStateStore } from 'oidc-client-ts';
import { useMemo, useState, useEffect, useCallback, createContext, useContext } from 'react';

import axios from 'src/lib/axios';
import { CONFIG } from 'src/global-config';

import { AuthContext } from '../auth-context';

// ----------------------------------------------------------------------

type KeycloakContextType = {
  userManager: UserManager | null;
  signIn: () => Promise<void>;
  signOut: () => Promise<void>;
  signInCallback: () => Promise<User | null>;
  signOutCallback: () => Promise<void>;
  removeUser: () => Promise<void>;
  getUser: () => Promise<User | null>;
};

const KeycloakContext = createContext<KeycloakContextType | undefined>(undefined);

export const useKeycloak = () => {
  const context = useContext(KeycloakContext);
  if (!context) {
    throw new Error('useKeycloak must be used within a KeycloakProvider');
  }
  return context;
};

// ----------------------------------------------------------------------

type Props = {
  children: React.ReactNode;
};

export function AuthProvider({ children }: Props) {
  const { authority, clientId, redirectUri, postLogoutRedirectUri, scope } = CONFIG.keycloak;

  const [userManager, setUserManager] = useState<UserManager | null>(null);

  useEffect(() => {
    if (authority && clientId && redirectUri) {
      const manager = new UserManager({
        authority,
        client_id: clientId,
        redirect_uri: redirectUri,
        post_logout_redirect_uri: postLogoutRedirectUri,
        response_type: 'code',
        scope,
        automaticSilentRenew: true,
        silent_redirect_uri: `${window.location.origin}/silent-renew.html`,
        userStore: new WebStorageStateStore({ store: window.localStorage }),
        loadUserInfo: true,
      });

      setUserManager(manager);

      // Handle token expiration
      manager.events.addUserSignedOut(() => {
        console.log('User signed out');
      });

      manager.events.addSilentRenewError((error) => {
        console.error('Silent renew error:', error);
      });
    }
  }, [authority, clientId, redirectUri, postLogoutRedirectUri, scope]);

  const signIn = useCallback(async () => {
    if (userManager) {
      await userManager.signinRedirect();
    }
  }, [userManager]);

  const signOut = useCallback(async () => {
    if (userManager) {
      await userManager.signoutRedirect();
    }
  }, [userManager]);

  const signInCallback = useCallback(async (): Promise<User | null> => {
    if (userManager) {
      try {
        const user = await userManager.signinRedirectCallback();
        return user;
      } catch (error) {
        console.error('Sign in callback error:', error);
        return null;
      }
    }
    return null;
  }, [userManager]);

  const signOutCallback = useCallback(async () => {
    if (userManager) {
      await userManager.signoutRedirectCallback();
    }
  }, [userManager]);

  const removeUser = useCallback(async () => {
    if (userManager) {
      await userManager.removeUser();
    }
  }, [userManager]);

  const getUser = useCallback(async (): Promise<User | null> => {
    if (userManager) {
      return await userManager.getUser();
    }
    return null;
  }, [userManager]);

  const keycloakValue = useMemo(
    () => ({
      userManager,
      signIn,
      signOut,
      signInCallback,
      signOutCallback,
      removeUser,
      getUser,
    }),
    [userManager, signIn, signOut, signInCallback, signOutCallback, removeUser, getUser]
  );

  if (!(authority && clientId && redirectUri)) {
    return null;
  }

  return (
    <KeycloakContext.Provider value={keycloakValue}>
      <AuthProviderContainer>{children}</AuthProviderContainer>
    </KeycloakContext.Provider>
  );
}

// ----------------------------------------------------------------------

function AuthProviderContainer({ children }: Props) {
  const { userManager, getUser } = useKeycloak();

  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const checkUserSession = useCallback(async () => {
    try {
      setLoading(true);
      const currentUser = await getUser();

      if (currentUser && !currentUser.expired) {
        setUser(currentUser);
        // Set authorization header for axios
        axios.defaults.headers.common.Authorization = `Bearer ${currentUser.access_token}`;
      } else {
        setUser(null);
        delete axios.defaults.headers.common.Authorization;
      }
    } catch (error) {
      console.error('Check user session error:', error);
      setUser(null);
      delete axios.defaults.headers.common.Authorization;
    } finally {
      setLoading(false);
    }
  }, [getUser]);

  useEffect(() => {
    checkUserSession();
  }, [checkUserSession]);

  useEffect(() => {
    if (userManager) {
      // Listen for user loaded events
      userManager.events.addUserLoaded((loadedUser) => {
        setUser(loadedUser);
        axios.defaults.headers.common.Authorization = `Bearer ${loadedUser.access_token}`;
      });

      // Listen for user unloaded events
      userManager.events.addUserUnloaded(() => {
        setUser(null);
        delete axios.defaults.headers.common.Authorization;
      });

      // Listen for user signed out events
      userManager.events.addUserSignedOut(() => {
        setUser(null);
        delete axios.defaults.headers.common.Authorization;
      });
    }
  }, [userManager]);

  // ----------------------------------------------------------------------

  const authenticated = !!user && !user.expired;
  const unauthenticated = !user || !!user.expired;

  const memoizedValue = useMemo(
    () => ({
      user: user
        ? {
            ...user.profile,
            id: user.profile.sub,
            accessToken: user.access_token,
            displayName: user.profile.name || user.profile.preferred_username,
            email: user.profile.email,
            photoURL: user.profile.picture,
            role: user.profile.role ?? 'user',
          }
        : null,
      loading,
      authenticated,
      unauthenticated,
      checkUserSession,
    }),
    [user, loading, authenticated, unauthenticated, checkUserSession]
  );

  return <AuthContext.Provider value={memoizedValue}>{children}</AuthContext.Provider>;
}
