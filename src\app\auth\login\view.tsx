'use client';

import { z as zod } from 'zod';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useBoolean } from 'minimal-shared/hooks';
import { zodResolver } from '@hookform/resolvers/zod';

import Box from '@mui/material/Box';
import Link from '@mui/material/Link';
import Alert from '@mui/material/Alert';
import IconButton from '@mui/material/IconButton';
import LoadingButton from '@mui/lab/LoadingButton';
import InputAdornment from '@mui/material/InputAdornment';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import { login as loginApi } from 'src/actions/ttu-api';

import { CONFIG } from 'src/global-config';

import { Iconify } from 'src/components/iconify';
import { Form, Field } from 'src/components/hook-form';

import { useAuthContext } from 'src/auth/hooks';
import { FormHead } from 'src/auth/components/form-head';
import { KeycloakLogin } from 'src/auth/components/keycloak-login';

// ----------------------------------------------------------------------

export type LoginSchemaType = zod.infer<typeof LoginSchema>;

export const LoginSchema = zod.object({
  email: zod
    .string()
    .min(1, { message: 'Vui lòng nhập email!' })
    .email({ message: 'Email không hợp lệ!' }),
  password: zod
    .string()
    .min(1, { message: 'Vui lòng nhập mật khẩu!' })
    .min(6, { message: 'Mật khẩu phải có ít nhất 6 ký tự!' }),
});

// ----------------------------------------------------------------------

export function LoginView() {
  // If using Keycloak, show Keycloak login component
  if (CONFIG.auth.method === 'keycloak') {
    return <KeycloakLogin />;
  }

  const { checkUserSession } = useAuthContext();
  const showPassword = useBoolean();

  const defaultValues = {
    email: '',
    password: '',
  };

  const methods = useForm<LoginSchemaType>({
    resolver: zodResolver(LoginSchema),
    defaultValues,
  });

  const {
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const onSubmit = handleSubmit(async (data) => {
    setErrorMessage(null);
    try {
      // Call real login API
      const res = await loginApi({ email: data.email, password: data.password });
      const payload = (res as any)?.data;
      const token = payload?.data?.token; // API: { code, message, data: { token, user, ... } }

      if (!token) {
        throw new Error(payload?.message || 'Không nhận được token từ máy chủ');
      }

      // Persist to localStorage
      localStorage.setItem('accessToken', token);
      try {
        localStorage.setItem('currentUser', JSON.stringify(payload?.data?.user ?? null));
        localStorage.setItem(
          'roles',
          JSON.stringify({
            is_admin: payload?.data?.is_admin,
            is_hr: payload?.data?.is_hr,
            is_post: payload?.data?.is_post,
          })
        );
      } catch {}

      // Update auth context then redirect
      if (typeof checkUserSession === 'function') {
        await checkUserSession();
      }
      // Use hard navigation to avoid any guard race conditions
      window.location.href = paths.dashboard.root;
    } catch (error: any) {
      const message =
        error?.message || error?.data?.message || 'Đăng nhập thất bại, vui lòng thử lại';
      setErrorMessage(message);
      console.error('Login error:', error);
    }
  });

  const renderForm = () => (
    <Box sx={{ gap: 3, display: 'flex', flexDirection: 'column' }}>
      <Field.Text
        name="email"
        label="Email"
        placeholder="Nhập email của bạn"
        InputLabelProps={{ shrink: true }}
      />

      <Field.Text
        name="password"
        label="Mật khẩu"
        placeholder="Nhập mật khẩu"
        type={showPassword.value ? 'text' : 'password'}
        InputLabelProps={{ shrink: true }}
        InputProps={{
          endAdornment: (
            <InputAdornment position="end">
              <IconButton onClick={showPassword.onToggle} edge="end">
                <Iconify icon={showPassword.value ? 'solar:eye-bold' : 'solar:eye-closed-bold'} />
              </IconButton>
            </InputAdornment>
          ),
        }}
      />

      <Link
        component={RouterLink}
        href="#"
        variant="body2"
        color="inherit"
        sx={{ alignSelf: 'flex-end' }}
      >
        Quên mật khẩu?
      </Link>

      <LoadingButton
        fullWidth
        color="inherit"
        size="large"
        type="submit"
        variant="contained"
        loading={isSubmitting}
        loadingIndicator="Đang đăng nhập..."
      >
        Đăng nhập
      </LoadingButton>
    </Box>
  );

  return (
    <>
      <FormHead
        title="Đăng nhập vào hệ thống"
        description="Hệ thống quản lý lớp học - Đại học Tân Tạo"
        sx={{ textAlign: { xs: 'center', md: 'left' } }}
      />

      <Alert severity="info" sx={{ mb: 3 }}>
        Sử dụng email và mật khẩu được cấp bởi phòng IT để đăng nhập
      </Alert>

      {!!errorMessage && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {errorMessage}
        </Alert>
      )}

      <Form methods={methods} onSubmit={onSubmit}>
        {renderForm()}
      </Form>
    </>
  );
}
