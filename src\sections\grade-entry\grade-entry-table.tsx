'use client';

import type { GradeComponent } from 'src/types/academic';

import { useState, useEffect, useCallback } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Chip from '@mui/material/Chip';
import Table from '@mui/material/Table';
import Alert from '@mui/material/Alert';
import Stack from '@mui/material/Stack';
import Avatar from '@mui/material/Avatar';
import Button from '@mui/material/Button';
import TableRow from '@mui/material/TableRow';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TextField from '@mui/material/TextField';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import CardHeader from '@mui/material/CardHeader';
import LoadingButton from '@mui/lab/LoadingButton';
import CircularProgress from '@mui/material/CircularProgress';

import { getEnroll, createComponentScore } from 'src/actions/ttu-api';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';

// ----------------------------------------------------------------------

const TABLE_HEAD = [
  { id: 'student_info', label: 'Sinh viên', width: 280 },
  { id: 'student_id', label: 'MSSV', width: 120 },
  { id: 'score', label: 'Điểm', width: 450 },
  { id: 'actions', label: 'Thao tác', width: 80 },
];

type StudentScore = {
  student_id: string;
  student_name: string;
  student_code: string;
  student_email: string;
  student_avatar?: string;
  score: string; // Changed to string to support text like "Vắng", "Không đạt"
  existing_score_id?: string;
  // Track original values to detect changes
  original_score?: string;
};

type Props = {
  classId: number;
  gradeComponentId: number;
  year: number;
  semester: number;
  gradeComponent: GradeComponent;
};

export function GradeEntryTable({
  classId,
  gradeComponentId,
  year,
  semester,
  gradeComponent,
}: Props) {
  const [students, setStudents] = useState<StudentScore[]>([]);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Score input component
  const ScoreInput = ({ student }: { student: StudentScore }) => {
    const [localScore, setLocalScore] = useState(student.score);

    // Sync local state with prop changes
    useEffect(() => {
      setLocalScore(student.score);
    }, [student.score]);

    const handleQuickScore = (value: string) => {
      setLocalScore(value);
      handleScoreChange(student.student_id, 'score', value);
    };

    const handleInputChange = (value: string) => {
      // Limit input to 5 characters
      const truncatedValue = value.substring(0, 5);
      setLocalScore(truncatedValue);
    };

    const handleInputBlur = () => {
      // Save to parent state when user finishes typing
      const validatedScore = localScore.trim();
      handleScoreChange(student.student_id, 'score', validatedScore);
    };

    return (
      <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
        {/* Main score input field */}
        <TextField
          size="small"
          value={localScore}
          onChange={(e) => handleInputChange(e.target.value)}
          onBlur={handleInputBlur}
          placeholder="Nhập điểm..."
          sx={{ width: 120 }}
          slotProps={{ htmlInput: { maxLength: 5 } }}
        />

        {/* "Khác" button with value "y" */}
        <Chip
          label="Khác"
          size="small"
          variant="outlined"
          onClick={() => handleQuickScore('i')}
          sx={{
            cursor: 'pointer',
            '&:hover': { backgroundColor: 'action.hover' },
            backgroundColor: student.score === 'y' ? 'action.selected' : 'transparent',
          }}
        />
      </Box>
    );
  };

  // Load students and existing scores
  const loadData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Load students enrolled in class using /student/enroll API
      const enrollResponse = await getEnroll(1, -1, classId);
      const enrolledStudents = enrollResponse?.data?.data?.data || [];

      // Load existing scores for this component using the new API
      let existingScores: any[] = [];
      try {
        const scoresResponse = await fetch(
          `https://apihr.ttu.edu.vn/v1/student/component-scores?page=1&perPage=-1&_class=${classId}&semester=${semester}`,
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('accessToken')}`,
              'Content-Type': 'application/json',
            },
          }
        );

        if (scoresResponse.ok) {
          const scoresData = await scoresResponse.json();
          existingScores = scoresData?.data?.data || [];
        }
      } catch (scoreError) {
        console.warn('Could not load existing scores:', scoreError);
        // Continue without existing scores
      }

      // Filter scores for current grade component and create score map for quick lookup
      const filteredScores = existingScores.filter(
        (score: any) => score.grade_component === gradeComponentId
      );
      const scoreMap = new Map<number, any>();
      filteredScores.forEach((score: any) => {
        scoreMap.set(score.student, score);
      });

      // Map students with existing scores
      const studentScores: StudentScore[] = enrolledStudents.map((enrollment: any) => {
        const student = enrollment.student;
        const existingScore = scoreMap.get(student._id);

        const currentScore = existingScore ? existingScore.ten_point_scale : '';

        return {
          student_id: student._id,
          student_name: `${student.last_name} ${student.first_name}`.trim(),
          student_code: student.student_id || '',
          student_email: student.email || '',
          student_avatar: student.avatar || '',
          score: currentScore,
          existing_score_id: existingScore ? existingScore._id : undefined,
          // Store original values to detect changes
          original_score: currentScore,
        };
      });

      setStudents(studentScores);
    } catch (err) {
      console.error('Error loading data:', err);
      setError('Không thể tải dữ liệu sinh viên và điểm số');
    } finally {
      setLoading(false);
    }
  }, [classId, gradeComponentId, semester]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  const handleScoreChange = (studentId: string, field: keyof StudentScore, value: string) => {
    setStudents((prev) =>
      prev.map((student) =>
        student.student_id === studentId ? { ...student, [field]: value } : student
      )
    );
  };

  const handleSaveAll = async () => {
    try {
      setSaving(true);

      // Helper function to check if student data has changed
      const hasChanged = (student: StudentScore) =>
        student.score !== (student.original_score || '');

      // Filter students: only those with scores AND changes
      const studentsToSave = students.filter((student) => {
        const hasScore = student.score !== '' && student.score.trim() !== '';
        const hasChanges = hasChanged(student);
        return hasScore && hasChanges;
      });

      console.log(
        `Saving ${studentsToSave.length} changed scores out of ${students.length} total students`
      );

      const promises = studentsToSave.map(async (student) => {
        // Validate and truncate score value to prevent database errors
        const scoreValue = String(student.score).trim();
        const truncatedScore = scoreValue.length > 5 ? scoreValue.substring(0, 5) : scoreValue;

        const scoreData = {
          year,
          semester,
          grade_component: gradeComponentId,
          class: classId,
          course: gradeComponent.course,
          student: Number(student.student_id),
          ten_point_scale: truncatedScore,
          description: '',
          note: '',
        };

        console.log('Sending score data:', scoreData);
        return createComponentScore(scoreData);
      });

      await Promise.all(promises);

      if (studentsToSave.length > 0) {
        toast.success(`Lưu thành công ${studentsToSave.length} điểm đã thay đổi!`);
      } else {
        toast.info('Không có điểm nào thay đổi để lưu');
      }
      // Reload data to get updated scores
      await loadData();
    } catch (err) {
      console.error('Error saving scores:', err);
      toast.error('Có lỗi xảy ra khi lưu điểm');
    } finally {
      setSaving(false);
    }
  };

  const handleClearAll = () => {
    setStudents((prev) =>
      prev.map((student) => ({
        ...student,
        score: '',
      }))
    );
  };

  const handleDeleteScore = async (student: StudentScore) => {
    if (!student.existing_score_id) {
      // If no existing score ID, just clear the local score
      handleScoreChange(student.student_id, 'score', '');
      return;
    }

    try {
      // Call DELETE API
      const response = await fetch(
        `https://apihr.ttu.edu.vn/v1/student/component-scores/${student.existing_score_id}`,
        {
          method: 'DELETE',
          headers: {
            Authorization: `Bearer ${localStorage.getItem('accessToken')}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.ok) {
        // Clear the score locally and remove existing_score_id
        setStudents((prev) =>
          prev.map((s) =>
            s.student_id === student.student_id
              ? { ...s, score: '', existing_score_id: undefined, original_score: '' }
              : s
          )
        );
        toast.success(`Đã xóa điểm của ${student.student_name}`);
      } else {
        throw new Error('Failed to delete score');
      }
    } catch (deleteError) {
      console.error('Error deleting score:', deleteError);
      toast.error('Có lỗi xảy ra khi xóa điểm');
    }
  };

  // Count valid scores and changed scores
  const validScoresCount = students.filter((s) => s.score !== '' && s.score.trim() !== '').length;

  const changedScoresCount = students.filter((student) => {
    const hasScore = student.score !== '' && student.score.trim() !== '';
    const hasChanges = student.score !== (student.original_score || '');
    return hasScore && hasChanges;
  }).length;

  if (loading) {
    return (
      <Card>
        <Box sx={{ p: 3, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <CircularProgress />
          <Typography sx={{ ml: 2 }}>Đang tải dữ liệu...</Typography>
        </Box>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <Alert severity="error" sx={{ m: 2 }}>
          {error}
        </Alert>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader
        sx={{ mb: 3 }}
        title={`Nhập điểm: ${gradeComponent.name_vie}`}
        subheader={`Trọng số: ${gradeComponent.weight}% • Đã nhập: ${validScoresCount}/${students.length} `}
        action={
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              color="warning"
              onClick={handleClearAll}
              disabled={saving}
              startIcon={<Iconify icon="solar:trash-bin-minimalistic-bold" />}
            >
              Xóa tất cả
            </Button>
            <LoadingButton
              variant="contained"
              onClick={handleSaveAll}
              loading={saving}
              disabled={changedScoresCount === 0}
              startIcon={<Iconify icon="solar:diskette-bold" />}
            >
              Lưu điểm ({changedScoresCount}/{validScoresCount})
            </LoadingButton>
          </Box>
        }
      />

      <Scrollbar>
        <Table size="small">
          <TableHead>
            <TableRow>
              {TABLE_HEAD.map((headCell) => (
                <TableCell key={headCell.id} sx={{ width: headCell.width }}>
                  {headCell.label}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {students.map((student) => (
              <TableRow key={student.student_id}>
                <TableCell>
                  <Stack direction="row" spacing={2} alignItems="center">
                    <Avatar
                      src={`${'https://intranet.ttu.edu.vn/uploads/'}${student.student_avatar}`}
                      alt={student.student_avatar}
                      sx={{ width: 40, height: 40 }}
                    >
                      {student.student_name.charAt(0)}
                    </Avatar>
                    <div>
                      <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                        {student.student_name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {student.student_email}
                      </Typography>
                    </div>
                  </Stack>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">{student.student_code}</Typography>
                </TableCell>
                <TableCell sx={{ minWidth: 300 }}>
                  <ScoreInput student={student} />
                </TableCell>
                <TableCell>
                  {student.score && (
                    <IconButton
                      size="small"
                      onClick={() => handleDeleteScore(student)}
                      color="error"
                      title="Xóa điểm"
                    >
                      <Iconify icon="solar:trash-bin-minimalistic-bold" />
                    </IconButton>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Scrollbar>

      {students.length === 0 && (
        <Box sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            Không có sinh viên nào trong lớp học này.
          </Typography>
        </Box>
      )}
    </Card>
  );
}
