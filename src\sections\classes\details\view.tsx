'use client';

import type { ClassWithStats } from 'src/types/academic';

import { useState, useEffect } from 'react';

import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import Card from '@mui/material/Card';

import { paths } from 'src/routes/paths';

import { DashboardContent } from 'src/layouts/dashboard';
import { fetchClassById } from 'src/actions/ttu-attendance-adapter';
import { fetchEnrollmentTotal } from 'src/actions/ttu-student-adapter';

import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';

import ClassOverviewTab from './class-overview-tab';
import ClassStudentsTab from './class-students-tab';
import ClassDocumentsTab from './class-documents-tab';

type Props = {
  classId: string;
};

export default function ClassDetailsView({ classId }: Props) {
  const [currentTab, setCurrentTab] = useState('overview');
  const [classData, setClassData] = useState<ClassWithStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadClassData = async () => {
      try {
        setLoading(true);
        const data = await fetchClassById(classId);
        if (data) {
          const total = await fetchEnrollmentTotal(classId);
          setClassData({ ...data, totalStudents: total });
        } else {
          setClassData(null);
        }
      } catch (error) {
        console.error('Error loading class data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadClassData();
  }, [classId]);

  const handleChangeTab = (event: React.SyntheticEvent, newValue: string) => {
    setCurrentTab(newValue);
  };

  if (loading) {
    return (
      <DashboardContent>
        <div>Đang tải...</div>
      </DashboardContent>
    );
  }

  if (!classData) {
    return (
      <DashboardContent>
        <div>Không tìm thấy lớp học</div>
      </DashboardContent>
    );
  }

  return (
    <DashboardContent>
      <CustomBreadcrumbs
        heading={`${classData.code} - ${classData.name}`}
        links={[
          { name: 'Trang chủ', href: paths.dashboard.root },
          { name: 'Danh sách lớp', href: paths.dashboard.classes.root },
          { name: classData.code },
        ]}
        // action={
        //   <Stack direction="row" spacing={1}>
        //     <Button
        //       component={RouterLink}
        //       href={`${paths.dashboard.attendance}?classId=${classId}`}
        //       variant="contained"
        //       startIcon={<Iconify icon="mingcute:add-line" />}
        //     >
        //       Tạo phiên điểm danh
        //     </Button>
        //     <Button
        //       component={RouterLink}
        //       href={paths.dashboard.classes.attendance(classId)}
        //       variant="outlined"
        //       startIcon={<Iconify icon="solar:clipboard-list-bold" />}
        //     >
        //       Xem điểm danh
        //     </Button>
        //     {classData && (
        //       <Button
        //         component={RouterLink}
        //         href={`${paths.dashboard.gradebook}?classId=${classId}&year=${classData.year}&semester=${classData.semester}`}
        //         variant="outlined"
        //         startIcon={<Iconify icon="solar:calculator-bold" />}
        //       >
        //         Quản lý điểm
        //       </Button>
        //     )}
        //   </Stack>
        // }
        sx={{ mb: { xs: 3, md: 5 } }}
      />

      <Card>
        <Tabs
          value={currentTab}
          onChange={handleChangeTab}
          sx={{
            px: 2.5,
            boxShadow: (theme) => `inset 0 -2px 0 0 ${theme.vars.palette.divider}`,
          }}
        >
          {[
            { value: 'overview', label: 'Tổng quan' },
            { value: 'students', label: 'Sinh viên' },
            { value: 'documents', label: 'Tài liệu' },
          ].map((tab) => (
            <Tab key={tab.value} value={tab.value} label={tab.label} />
          ))}
        </Tabs>

        {currentTab === 'overview' && <ClassOverviewTab classData={classData} />}
        {currentTab === 'students' && <ClassStudentsTab classId={classId} />}
        {currentTab === 'documents' && <ClassDocumentsTab classId={classId} />}
      </Card>
    </DashboardContent>
  );
}
