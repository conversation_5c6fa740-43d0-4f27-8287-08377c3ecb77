/**
 * Test configuration for Keycloak integration
 * This file helps verify that all required environment variables are set correctly
 */

import { CONFIG } from 'src/global-config';

export function validateKeycloakConfig() {
  const { authority, clientId, redirectUri, postLogoutRedirectUri, scope } = CONFIG.keycloak;
  
  const errors: string[] = [];
  
  if (!authority) {
    errors.push('NEXT_PUBLIC_KEYCLOAK_AUTHORITY is not set');
  } else if (!authority.includes('/realms/')) {
    errors.push('NEXT_PUBLIC_KEYCLOAK_AUTHORITY should include /realms/your-realm-name');
  }
  
  if (!clientId) {
    errors.push('NEXT_PUBLIC_KEYCLOAK_CLIENT_ID is not set');
  }
  
  if (!redirectUri) {
    errors.push('NEXT_PUBLIC_KEYCLOAK_REDIRECT_URI is not set');
  } else if (!redirectUri.includes('/auth/keycloak/callback')) {
    errors.push('NEXT_PUBLIC_KEYCLOAK_REDIRECT_URI should end with /auth/keycloak/callback');
  }
  
  if (!postLogoutRedirectUri) {
    errors.push('NEXT_PUBLIC_KEYCLOAK_POST_LOGOUT_REDIRECT_URI is not set');
  } else if (!postLogoutRedirectUri.includes('/auth/keycloak/logout-callback')) {
    errors.push('NEXT_PUBLIC_KEYCLOAK_POST_LOGOUT_REDIRECT_URI should end with /auth/keycloak/logout-callback');
  }
  
  if (!scope) {
    errors.push('NEXT_PUBLIC_KEYCLOAK_SCOPE is not set (default: openid profile email)');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    config: {
      authority,
      clientId,
      redirectUri,
      postLogoutRedirectUri,
      scope,
    },
  };
}

export function logKeycloakConfig() {
  const validation = validateKeycloakConfig();
  
  console.group('🔐 Keycloak Configuration');
  console.log('Auth method:', CONFIG.auth.method);
  console.log('Configuration:', validation.config);
  
  if (validation.isValid) {
    console.log('✅ Configuration is valid');
  } else {
    console.error('❌ Configuration errors:');
    validation.errors.forEach(error => console.error(`  - ${error}`));
  }
  
  console.groupEnd();
  
  return validation;
}
