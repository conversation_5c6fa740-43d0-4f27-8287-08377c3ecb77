'use client';

import type { GradeComponent, CreateGradeComponentForm } from 'src/types/academic';

import { z as zod } from 'zod';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useState, useEffect, useCallback } from 'react';

import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import TextField from '@mui/material/TextField';
import LoadingButton from '@mui/lab/LoadingButton';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';

import { createGradeComponent, updateGradeComponent } from 'src/actions/ttu-api';

import { toast } from 'src/components/snackbar';

// ----------------------------------------------------------------------

const GradeComponentSchema = zod.object({
  name_vie: zod.string().min(1, 'Tên tiếng Việt là bắt buộc'),
  name_eng: zod.string().min(1, 'Tên tiếng Anh là bắt buộc'),
  weight: zod
    .number()
    .min(1, 'Trọng số phải lớn hơn 0')
    .max(100, 'Trọng số không được vượt quá 100'),
  description: zod.string().optional(),
  note: zod.string().optional(),
});

type FormData = zod.infer<typeof GradeComponentSchema>;

type Props = {
  open: boolean;
  onClose: () => void;
  classId: number | null;
  classData?: {
    year: number;
    semester: number;
    course: number;
  };
  onSuccess?: () => void;
  editComponent?: GradeComponent | null;
};

export function GradeComponentDialog({
  open,
  onClose,
  classId,
  classData,
  onSuccess,
  editComponent,
}: Props) {
  const [loading, setLoading] = useState(false);

  const isEditMode = Boolean(editComponent);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<FormData>({
    resolver: zodResolver(GradeComponentSchema),
    defaultValues: {
      name_vie: '',
      name_eng: '',
      weight: 0,
      description: '',
      note: '',
    },
  });

  // Reset form when dialog opens/closes or when editComponent changes
  useEffect(() => {
    if (open) {
      if (editComponent) {
        // Populate form with edit data
        reset({
          name_vie: editComponent.name_vie || '',
          name_eng: editComponent.name_eng || '',
          weight: editComponent.weight || 0,
          description: editComponent.description || '',
          note: editComponent.note || '',
        });
      } else {
        // Reset to empty form for create mode
        reset({
          name_vie: '',
          name_eng: '',
          weight: 0,
          description: '',
          note: '',
        });
      }
    }
  }, [open, editComponent, reset]);

  const onSubmit = useCallback(
    async (data: FormData) => {
      if (!classId || !classData) {
        toast.error('Vui lòng chọn lớp học');
        return;
      }

      try {
        setLoading(true);

        if (isEditMode && editComponent) {
          // Update existing component
          const updatePayload = {
            name_eng: data.name_eng,
            name_vie: data.name_vie,
            weight: data.weight,
            description: data.description || '',
            note: data.note || '',
          };

          await updateGradeComponent(editComponent._id, updatePayload);
          toast.success('Cập nhật thành phần điểm thành công!');
        } else {
          // Create new component
          const payload: CreateGradeComponentForm = {
            year: classData.year,
            semester: classData.semester,
            class: classId,
            course: classData.course,
            name_eng: data.name_eng,
            name_vie: data.name_vie,
            weight: data.weight,
            description: data.description || '',
            note: data.note || '',
          };

          await createGradeComponent(payload);
          toast.success('Tạo thành phần điểm thành công!');
        }

        reset();
        onClose();
        onSuccess?.();
      } catch (error) {
        console.error('Error handling grade component:', error);
        toast.error('Có lỗi xảy ra, vui lòng thử lại');
      } finally {
        setLoading(false);
      }
    },
    [classId, classData, isEditMode, editComponent, reset, onClose, onSuccess]
  );

  const handleClose = useCallback(() => {
    reset();
    onClose();
  }, [reset, onClose]);

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>{isEditMode ? 'Sửa thành phần điểm' : 'Thêm thành phần điểm'}</DialogTitle>

      <DialogContent>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, pt: 1 }}>
          <Controller
            name="name_vie"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label="Tên tiếng Việt"
                placeholder="Ví dụ: Cuối kỳ"
                error={!!errors.name_vie}
                helperText={errors.name_vie?.message}
                fullWidth
              />
            )}
          />

          <Controller
            name="name_eng"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label="Tên tiếng Anh"
                placeholder="Ví dụ: Final exam"
                error={!!errors.name_eng}
                helperText={errors.name_eng?.message}
                fullWidth
              />
            )}
          />

          <Controller
            name="weight"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label="Trọng số (%)"
                type="number"
                placeholder="Ví dụ: 30"
                error={!!errors.weight}
                helperText={errors.weight?.message}
                onChange={(e) => field.onChange(Number(e.target.value))}
                fullWidth
              />
            )}
          />

          <Controller
            name="description"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label="Mô tả"
                placeholder="Mô tả chi tiết về thành phần điểm này"
                multiline
                rows={3}
                fullWidth
              />
            )}
          />

          <Controller
            name="note"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                label="Ghi chú"
                placeholder="Ghi chú thêm (nếu có)"
                multiline
                rows={2}
                fullWidth
              />
            )}
          />
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose} disabled={loading}>
          Hủy
        </Button>
        <LoadingButton variant="contained" onClick={handleSubmit(onSubmit)} loading={loading}>
          {isEditMode ? 'Cập nhật' : 'Tạo thành phần điểm'}
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
}
