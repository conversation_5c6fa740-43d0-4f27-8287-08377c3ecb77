const ROOTS = {
  AUTH: '/auth',
  DASHBOARD: '/dashboard',
};

export const paths = {
  // Common error pages
  page403: '/error/403',
  page404: '/error/404',
  page500: '/error/500',

  // AUTH
  auth: {
    login: `${ROOTS.AUTH}/login`,
  },

  // DASHBOARD (trimmed to only what we use)
  dashboard: {
    root: ROOTS.DASHBOARD,

    // Academic Management
    classes: {
      root: `${ROOTS.DASHBOARD}/classes`,
      details: (id: string) => `${ROOTS.DASHBOARD}/classes/${id}`,
      attendance: (id: string) => `${ROOTS.DASHBOARD}/classes/${id}/attendance`,
      attendanceSession: (classId: string, sessionId: string) =>
        `${ROOTS.DASHBOARD}/classes/${classId}/attendance/${sessionId}`,
    },

    reports: `${ROOTS.DASHBOARD}/reports`,
    attendance: `${ROOTS.DASHBOARD}/attendance`,
    attendanceHistory: `${ROOTS.DASHBOARD}/attendance-history`,
    students: `${ROOTS.DASHBOARD}/students`,
    studentReports: `${ROOTS.DASHBOARD}/student-reports`,
    semesters: `${ROOTS.DASHBOARD}/semesters`,
    gradebook: `${ROOTS.DASHBOARD}/gradebook`,
    gradeEntry: `${ROOTS.DASHBOARD}/grade-entry`,
  },
};
