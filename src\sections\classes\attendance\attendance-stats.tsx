'use client';

import Card from '@mui/material/Card';
import CardHeader from '@mui/material/CardHeader';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

import type { ClassWithStats, AttendanceSessionWithRecords } from 'src/types/academic';

export default function AttendanceStats({
  classData,
  sessions,
}: {
  classData: ClassWithStats;
  sessions: AttendanceSessionWithRecords[];
}) {
  const total = classData.totalStudents ?? classData.size ?? 0;
  const present = sessions.reduce((acc, s) => acc + (s.presentCount ?? 0), 0);
  const absent = sessions.reduce((acc, s) => acc + (s.absentCount ?? 0), 0);

  return (
    <Card>
      <CardHeader title="Thống kê chuyên cần (tổng hợp)" />
      <Stack direction={{ xs: 'column', md: 'row' }} spacing={3} sx={{ p: 3 }}>
        <Stack>
          <Typography variant="h3">{total}</Typography>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            Sĩ số lớp
          </Typography>
        </Stack>
        <Stack>
          <Typography variant="h3">{present}</Typography>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            Tổng lượt có mặt
          </Typography>
        </Stack>
        <Stack>
          <Typography variant="h3">{absent}</Typography>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            Tổng lượt vắng
          </Typography>
        </Stack>
      </Stack>
    </Card>
  );
}
