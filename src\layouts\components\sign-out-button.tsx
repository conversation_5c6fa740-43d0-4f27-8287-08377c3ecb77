import type { ButtonProps } from '@mui/material/Button';

import { useCallback } from 'react';
import { useAuth0 } from '@auth0/auth0-react';

import Button from '@mui/material/Button';

import { paths } from 'src/routes/paths';

import { CONFIG } from 'src/global-config';

import { toast } from 'src/components/snackbar';

import { useAuthContext } from 'src/auth/hooks';
import { signOut as jwtSignOut } from 'src/auth/context/jwt/action';
import { signOut as amplifySignOut } from 'src/auth/context/amplify/action';
import { signOut as supabaseSignOut } from 'src/auth/context/supabase/action';
import { signOut as firebaseSignOut } from 'src/auth/context/firebase/action';
import { signOut as keycloakSignOut } from 'src/auth/context/keycloak/action';

// ----------------------------------------------------------------------

const signOut =
  (CONFIG.auth.method === 'supabase' && supabaseSignOut) ||
  (CONFIG.auth.method === 'firebase' && firebaseSignOut) ||
  (CONFIG.auth.method === 'amplify' && amplifySignOut) ||
  (CONFIG.auth.method === 'keycloak' && keycloakSignOut) ||
  jwtSignOut;

type Props = ButtonProps & {
  onClose?: () => void;
};

export function SignOutButton({ onClose, sx, ...other }: Props) {
  const { checkUserSession } = useAuthContext();

  const { logout: signOutAuth0 } = useAuth0();

  const handleLogout = useCallback(async () => {
    try {
      await signOut();
      await checkUserSession?.();

      // Clear all auth-related data from storage
      sessionStorage.clear();
      localStorage.removeItem('currentUser');
      localStorage.removeItem('accessToken');
      localStorage.removeItem('user');
      localStorage.removeItem('token');
      localStorage.removeItem('jwt_access_token');
      sessionStorage.removeItem('jwt_access_token');

      onClose?.();

      toast.success('Đăng xuất thành công!');

      // Force reload to clear all app state and redirect to login
      window.location.href = paths.auth.login;
    } catch (error) {
      console.error(error);
      toast.error('Không thể đăng xuất!');
    }
  }, [checkUserSession, onClose]);

  const handleLogoutAuth0 = useCallback(async () => {
    try {
      await signOutAuth0();

      // Clear all auth-related data from storage
      sessionStorage.clear();
      localStorage.removeItem('currentUser');
      localStorage.removeItem('accessToken');
      localStorage.removeItem('user');
      localStorage.removeItem('token');
      localStorage.removeItem('jwt_access_token');
      sessionStorage.removeItem('jwt_access_token');

      onClose?.();

      toast.success('Đăng xuất thành công!');

      // Force reload to clear all app state and redirect to login
      window.location.href = paths.auth.login;
    } catch (error) {
      console.error(error);
      toast.error('Không thể đăng xuất!');
    }
  }, [onClose, signOutAuth0]);

  return (
    <Button
      fullWidth
      variant="soft"
      size="large"
      color="error"
      onClick={CONFIG.auth.method === 'auth0' ? handleLogoutAuth0 : handleLogout}
      sx={sx}
      {...other}
    >
      Đăng xuất
    </Button>
  );
}
