import i18next from 'i18next';

import { fallbackLng } from '../locales-config';

import type { languages } from '../locales-config';

// ----------------------------------------------------------------------

type LanguageValue = (typeof languages)[number];

const numberFormats: Record<LanguageValue, { code: string; currency: string }> = {
  en: { code: 'en-US', currency: 'USD' },
  fr: { code: 'fr-FR', currency: 'EUR' },
  vi: { code: 'vi-VN', currency: 'VND' },
  cn: { code: 'zh-CN', currency: 'CNY' },
  ar: { code: 'ar', currency: 'AED' },
};

export function formatNumberLocale() {
  const lng = (i18next.resolvedLanguage as LanguageValue) ?? (fallbackLng as LanguageValue);
  const fmt = numberFormats[lng] ?? numberFormats[fallbackLng as LanguageValue];
  return { code: fmt.code, currency: fmt.currency };
}
