'use client';

import Box from '@mui/material/Box';
import List from '@mui/material/List';
import Link from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import ListItem from '@mui/material/ListItem';
import Typography from '@mui/material/Typography';

import { Iconify } from 'src/components/iconify';

import type { ClassWithStats } from 'src/types/academic';

type Props = { classId: string };

export default function ClassDocumentsTab({ classId }: Props) {
  // Placeholder until document API is available
  const docs = [
    { id: '1', name: '<PERSON><PERSON> cương môn học.pdf', url: '#' },
    { id: '2', name: 'Tài liệu buổi 1.pptx', url: '#' },
    { id: '3', name: 'Danh sách nhóm.xlsx', url: '#' },
  ];

  return (
    <Box sx={{ p: 3 }}>
      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 2 }}>
        <Typography variant="h6">T<PERSON><PERSON> liệu lớp học</Typography>
        <Button variant="contained" startIcon={<Iconify icon="solar:upload-bold" />}>
          Tải tài liệu lên
        </Button>
      </Stack>

      <List>
        {docs.map((d) => (
          <ListItem key={d.id} sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Link href={d.url} target="_blank" sx={{ cursor: 'pointer' }}>
              {d.name}
            </Link>
            <Button
              size="small"
              color="error"
              startIcon={<Iconify icon="solar:trash-bin-trash-bold" />}
            >
              Xóa
            </Button>
          </ListItem>
        ))}
      </List>
    </Box>
  );
}
