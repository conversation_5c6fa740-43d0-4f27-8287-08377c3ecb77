'use client';

import { useCallback } from 'react';
import TextField from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';
import Stack from '@mui/material/Stack';

import { Iconify } from 'src/components/iconify';

type Props = {
  search: string;
  onSearchChange: (value: string) => void;
};

export default function StudentTableToolbar({ search, onSearchChange }: Props) {
  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => onSearchChange(e.target.value),
    [onSearchChange]
  );

  return (
    <Stack sx={{ p: 2 }}>
      <TextField
        value={search}
        onChange={handleChange}
        placeholder="Tìm kiếm theo MSSV hoặc Họ tên..."
        slotProps={{
          input: {
            startAdornment: (
              <InputAdornment position="start">
                <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled' }} />
              </InputAdornment>
            ),
          },
        }}
      />
    </Stack>
  );
}
