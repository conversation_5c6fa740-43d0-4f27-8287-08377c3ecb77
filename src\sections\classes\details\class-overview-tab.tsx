import type { ClassWithStats } from 'src/types/academic';

import { useState, useEffect } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import CardContent from '@mui/material/CardContent';

import { paths } from 'src/routes/paths';
import { RouterLink } from 'src/routes/components';

import axios from 'src/lib/axios';
import { getGradeComponents } from 'src/actions/ttu-api';
import { fetchAttendanceSessionsByClass } from 'src/actions/ttu-attendance-adapter';

import { Iconify } from 'src/components/iconify';

type Props = { classData: ClassWithStats & { year?: number; semester?: number } };

export default function ClassOverviewTab({ classData }: Props) {
  const [stats, setStats] = useState({
    totalStudents: classData.totalStudents ?? classData.size ?? 0,
    attendanceRate: 0,
    gradeComponentsCount: 0,
    attendanceSessionsCount: 0,
  });
  const [loading, setLoading] = useState(false);

  // Load real data for stats
  useEffect(() => {
    const loadStats = async () => {
      if (!classData.id) return;

      console.log('Loading stats for class:', {
        id: classData.id,
        year: classData.year,
        semester: classData.semester,
        classData,
      });

      try {
        setLoading(true);

        // Load grade components count
        let gradeComponentsCount = 0;
        // Use current year/semester as fallback if not available in classData
        const currentYear = classData.year || new Date().getFullYear();
        const currentSemester = classData.semester || 1;

        const components = await getGradeComponents({
          year: currentYear,
          semester: currentSemester,
          _class: parseInt(classData.id, 10),
          page: 1,
          perPage: -1,
        });
        console.log('Grade components response:', components);
        gradeComponentsCount = components?.data?.data?.data?.length || 0;

        // Load attendance sessions
        const sessions = await fetchAttendanceSessionsByClass(classData.id);
        const attendanceSessionsCount = sessions?.length || 0;

        // Calculate attendance rate using absent data (like in students tab)
        let attendanceRate = 0;
        try {
          // Get absent data for the class
          const absentResponse = await axios.get('/student/absent/list-absence', {
            params: { page: 1, perPage: -1, _class: parseInt(classData.id, 10) },
          });

          const absentList = absentResponse.data?.data?.data || [];
          console.log('Absent data:', absentList);

          // Get total students count
          const totalStudents = classData.totalStudents ?? classData.size ?? 0;

          if (totalStudents > 0 && attendanceSessionsCount > 0) {
            // Calculate total possible attendances
            const totalPossibleAttendances = totalStudents * attendanceSessionsCount;

            // Calculate total absences
            const totalAbsences = absentList.reduce(
              (sum: number, item: any) => sum + (item.number_of_absence || 0),
              0
            );

            // Calculate attendance rate: (total possible - total absences) / total possible * 100
            const actualAttendances = totalPossibleAttendances - totalAbsences;
            attendanceRate = Math.round((actualAttendances / totalPossibleAttendances) * 100);

            console.log('Attendance calculation:', {
              totalStudents,
              attendanceSessionsCount,
              totalPossibleAttendances,
              totalAbsences,
              actualAttendances,
              attendanceRate,
            });
          }
        } catch (error) {
          console.error('Error calculating attendance rate:', error);
          // Fallback to simple calculation if absent API fails
          if (sessions && sessions.length > 0) {
            const totalAttendances = sessions.reduce(
              (sum: number, session: any) => sum + (session.attendanceCount || 0),
              0
            );
            const totalPossible =
              sessions.length * (classData.totalStudents ?? classData.size ?? 1);
            attendanceRate =
              totalPossible > 0 ? Math.round((totalAttendances / totalPossible) * 100) : 0;
          }
        }

        setStats({
          totalStudents: classData.totalStudents ?? classData.size ?? 0,
          attendanceRate,
          gradeComponentsCount,
          attendanceSessionsCount,
        });
      } catch (error) {
        console.error('Error loading stats:', error);
      } finally {
        setLoading(false);
      }
    };

    loadStats();
  }, [classData.id, classData.year, classData.semester, classData.totalStudents, classData.size]);

  const rows: Array<{ label: string; value: string | number }> = [
    { label: 'Mã lớp', value: classData.code },
    { label: 'Tên học phần', value: classData.name },
    {
      label: 'Học kỳ',
      value:
        classData.term === '1'
          ? 'Fall'
          : classData.term === '2'
            ? 'Spring'
            : classData.term === '3'
              ? 'Summer'
              : classData.term,
    },
    { label: 'Sĩ số', value: stats.totalStudents },
  ];

  return (
    <Box sx={{ p: 3 }}>
      {/* Header Section */}
      <Box sx={{ mb: 4 }}>
        {/* Stats Cards */}
        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: { xs: 'repeat(2, 1fr)', sm: 'repeat(4, 1fr)' },
            gap: 2,
          }}
        >
          <Card sx={{ textAlign: 'center', p: 2, backgroundColor: 'primary.lighter' }}>
            <Iconify
              icon="solar:users-group-rounded-bold"
              width={32}
              sx={{ color: 'primary.main', mb: 1 }}
            />
            <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main' }}>
              {loading ? '...' : stats.totalStudents}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Sinh viên
            </Typography>
          </Card>

          <Card sx={{ textAlign: 'center', p: 2, backgroundColor: 'success.lighter' }}>
            <Iconify
              icon="solar:clipboard-check-bold"
              width={32}
              sx={{ color: 'success.main', mb: 1 }}
            />
            <Typography variant="h4" sx={{ fontWeight: 700, color: 'success.main' }}>
              {loading ? '...' : `${stats.attendanceRate}%`}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Tỷ lệ điểm danh
            </Typography>
          </Card>

          <Card sx={{ textAlign: 'center', p: 2, backgroundColor: 'info.lighter' }}>
            <Iconify icon="solar:calculator-bold" width={32} sx={{ color: 'info.main', mb: 1 }} />
            <Typography variant="h4" sx={{ fontWeight: 700, color: 'info.main' }}>
              {loading ? '...' : stats.gradeComponentsCount}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Thành phần điểm
            </Typography>
          </Card>

          <Card sx={{ textAlign: 'center', p: 2, backgroundColor: 'warning.lighter' }}>
            <Iconify icon="solar:calendar-bold" width={32} sx={{ color: 'warning.main', mb: 1 }} />
            <Typography variant="h4" sx={{ fontWeight: 700, color: 'warning.main' }}>
              {loading ? '...' : stats.attendanceSessionsCount}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Phiên điểm danh
            </Typography>
          </Card>
        </Box>
      </Box>

      <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 3 }}>
        {/* Detailed Info */}
        <Box sx={{ flex: 1 }}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography
                variant="h6"
                sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}
              >
                <Iconify icon="solar:document-text-bold" width={24} />
                Chi tiết lớp học
              </Typography>
              <Stack spacing={3}>
                {rows.map((r) => (
                  <Box
                    key={r.label}
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      backgroundColor: 'grey.50',
                      border: '1px solid',
                      borderColor: 'grey.200',
                    }}
                  >
                    <Stack direction="row" justifyContent="space-between" alignItems="center">
                      <Typography variant="body2" sx={{ color: 'text.secondary', fontWeight: 500 }}>
                        {r.label}
                      </Typography>
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        {r.value}
                      </Typography>
                    </Stack>
                  </Box>
                ))}
              </Stack>
            </CardContent>
          </Card>
        </Box>

        {/* Action Cards */}
        <Box sx={{ flex: 1 }}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography
                variant="h6"
                sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}
              >
                <Iconify icon="solar:widget-bold" width={24} />
                Hành động nhanh
              </Typography>

              <Box
                sx={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(2, 1fr)',
                  gap: 2,
                }}
              >
                {/* Create Attendance */}
                <Box
                  sx={{
                    transition: 'all 0.3s ease',
                    borderRadius: 2,
                    border: '1px solid',
                    borderColor: 'grey.200',
                    p: 2,
                    '&:hover': {
                      backgroundColor: 'primary.lighter',
                    },
                  }}
                >
                  <Card
                    sx={{ textDecoration: 'none!important' }}
                    component={RouterLink}
                    href={`${paths.dashboard.attendance}?classId=${classData.id}`}
                  >
                    <Stack spacing={1.5} alignItems="center" textAlign="center">
                      <Box
                        sx={{
                          width: 56,
                          height: 56,
                          borderRadius: '50%',
                          backgroundColor: 'primary.main',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <Iconify icon="mingcute:add-line" width={28} sx={{ color: 'white' }} />
                      </Box>
                      <Box>
                        <Typography variant="subtitle2" sx={{ fontWeight: 700, mb: 0.5 }}>
                          Tạo phiên điểm danh
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Bắt đầu điểm danh mới
                        </Typography>
                      </Box>
                    </Stack>
                  </Card>
                </Box>

                {/* View Attendance */}
                <Box
                  sx={{
                    transition: 'all 0.3s ease',
                    borderRadius: 2,
                    border: '1px solid',
                    borderColor: 'grey.200',
                    p: 2,
                    '&:hover': {
                      backgroundColor: 'primary.lighter',
                    },
                  }}
                >
                  <Card
                    sx={{ textDecoration: 'none!important' }}
                    component={RouterLink}
                    href={paths.dashboard.classes.attendance(classData.id)}
                  >
                    <Stack spacing={1.5} alignItems="center" textAlign="center">
                      <Box
                        sx={{
                          width: 56,
                          height: 56,
                          borderRadius: '50%',
                          backgroundColor: 'info.main',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <Iconify
                          icon="solar:clipboard-list-bold"
                          width={28}
                          sx={{ color: 'white' }}
                        />
                      </Box>
                      <Box>
                        <Typography variant="subtitle2" sx={{ fontWeight: 700, mb: 0.5 }}>
                          Xem lịch sử
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Lịch sử điểm danh
                        </Typography>
                      </Box>
                    </Stack>
                  </Card>
                </Box>

                {/* Manage Grades */}
                {classData.year && classData.semester && (
                  <Box
                    sx={{
                      transition: 'all 0.3s ease',
                      borderRadius: 2,
                      border: '1px solid',
                      borderColor: 'grey.200',
                      p: 2,
                      '&:hover': {
                        backgroundColor: 'primary.lighter',
                      },
                    }}
                  >
                    <Card
                      sx={{ textDecoration: 'none!important' }}
                      component={RouterLink}
                      href={`${paths.dashboard.gradebook}?classId=${classData.id}&year=${classData.year}&semester=${classData.semester}`}
                    >
                      <Stack spacing={1.5} alignItems="center" textAlign="center">
                        <Box
                          sx={{
                            width: 56,
                            height: 56,
                            borderRadius: '50%',
                            backgroundColor: 'success.main',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                          }}
                        >
                          <Iconify
                            icon="solar:calculator-bold"
                            width={28}
                            sx={{ color: 'white' }}
                          />
                        </Box>
                        <Box>
                          <Typography variant="subtitle2" sx={{ fontWeight: 700, mb: 0.5 }}>
                            Quản lý thành phần
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Thiết lập cấu trúc điểm
                          </Typography>
                        </Box>
                      </Stack>
                    </Card>
                  </Box>
                )}

                {/* Enter Grades */}
                <Box
                  sx={{
                    transition: 'all 0.3s ease',
                    borderRadius: 2,
                    border: '1px solid',
                    borderColor: 'grey.200',
                    p: 2,
                    '&:hover': {
                      backgroundColor: 'primary.lighter',
                    },
                  }}
                >
                  <Card
                    sx={{ textDecoration: 'none!important' }}
                    component={RouterLink}
                    href={paths.dashboard.gradeEntry}
                  >
                    <Stack spacing={1.5} alignItems="center" textAlign="center">
                      <Box
                        sx={{
                          width: 56,
                          height: 56,
                          borderRadius: '50%',
                          backgroundColor: 'warning.main',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <Iconify
                          icon="solar:pen-new-square-bold"
                          width={28}
                          sx={{ color: 'white' }}
                        />
                      </Box>
                      <Box>
                        <Typography variant="subtitle2" sx={{ fontWeight: 700, mb: 0.5 }}>
                          Nhập điểm
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Nhập điểm sinh viên
                        </Typography>
                      </Box>
                    </Stack>
                  </Card>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Box>
      </Box>
    </Box>
  );
}
