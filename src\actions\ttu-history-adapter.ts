import { deleteAttendance, getAttendanceHistory } from './ttu-api';

export type AttendanceHistoryItem = {
  id: string;
  title: string;
  content: string;
  className: string;
  classCode: string;
  classId: string;
  date: string;
  createdAt: string;
  createdBy: string;
  semester: number;
  year: number;
  locked: boolean;
};

export type AttendanceHistoryResponse = {
  items: AttendanceHistoryItem[];
  total: number;
  page: number;
  perPage: number;
};

export type HistoryFilters = {
  year?: string | number;
  semester?: string | number;
  classId?: string;
  page?: number;
  perPage?: number;
};

export function mapAttendanceToHistoryItem(raw: any): AttendanceHistoryItem {
  const classInfo = raw.class || {};

  return {
    id: raw._id || raw.id || '',
    title: `${classInfo.name || 'Lớp học'} - ${raw.code || ''}`,
    content: raw.content || 'Điểm danh',
    className: classInfo.name || 'Lớp học',
    classCode: raw.code || classInfo.code || '',
    classId: classInfo._id || classInfo.id || '',
    date: raw.date || raw.created_at || '',
    createdAt: raw.created_at || '',
    createdBy: raw.created_by || 'Hệ thống',
    semester: raw.semester || 1,
    year: raw.year || new Date().getFullYear(),
    locked: raw.locked || false,
  };
}

export async function fetchAttendanceHistory(
  filters: HistoryFilters = {}
): Promise<AttendanceHistoryResponse> {
  const { page = 1, perPage = 10, year, semester, classId } = filters;

  const params: Record<string, any> = {};

  if (year) params.year = year;
  if (semester) params.semester = semester;
  if (classId) params._class = classId;

  const res = await getAttendanceHistory(page, perPage, params);
  const payload = (res as any)?.data;

  // API response structure: { code: 200, data: { total: number, data: [...] } }
  const responseData = payload?.data || payload;
  const items = responseData?.data || responseData?.items || [];
  const total = responseData?.total || 0;

  return {
    items: items.map(mapAttendanceToHistoryItem),
    total,
    page,
    perPage,
  };
}

export async function deleteAttendanceHistory(attendanceId: string): Promise<void> {
  await deleteAttendance(attendanceId);
}

// Helper functions
export function getSemesterLabel(semester: number): string {
  switch (semester) {
    case 1:
      return 'Fall';
    case 2:
      return 'Spring';
    case 3:
      return 'Summer';
    default:
      return `Học kỳ ${semester}`;
  }
}

export function formatDate(dateString: string): string {
  try {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  } catch {
    return dateString;
  }
}

export function formatDateTime(dateString: string): string {
  try {
    return new Date(dateString).toLocaleString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  } catch {
    return dateString;
  }
}

export function getTimeAgo(dateString: string): string {
  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();

    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMinutes < 60) {
      return `${diffMinutes} phút trước`;
    } else if (diffHours < 24) {
      return `${diffHours} giờ trước`;
    } else if (diffDays < 30) {
      return `${diffDays} ngày trước`;
    } else {
      return formatDate(dateString);
    }
  } catch {
    return dateString;
  }
}
