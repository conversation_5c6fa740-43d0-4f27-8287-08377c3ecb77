# Hướng dẫn từng bước khắc phục lỗi Keycloak Logout

## 🎯 Mục tiêu
<PERSON> phục lỗi "Invalid redirect uri" khi logout từ ứng dụng.

## 📋 Chuẩn bị
- Keycloak đang chạy tại `http://localhost:8080`
- Ứng dụng Next.js đang chạy tại `http://localhost:8082`
- <PERSON><PERSON> quyền admin trên Keycloak

## 🔧 Bước 1: Kiểm tra vấn đề hiện tại

### 1.1 Test logout URL
1. Truy cập: `http://localhost:8082/auth/keycloak/test-logout`
2. Click "Generate Test URL"
3. Click "Test This URL"
4. **N<PERSON>u thấy "We are sorry... Invalid redirect uri"** → Tiếp tục bước 2

### 1.2 Kiểm tra cấu hình hiện tại
1. Truy cập: `http://localhost:8082/auth/keycloak/config-check`
2. <PERSON><PERSON> các cấu hình environment variables
3. Note lại URL cần thiết

## 🛠️ Bước 2: Cấu hình Keycloak Client

### 2.1 Đăng nhập Keycloak Admin
1. Mở browser, truy cập: `http://localhost:8080/admin`
2. Đăng nhập với admin credentials
3. Chọn realm **"ttu"** (không phải Master)

### 2.2 Tìm và mở Client
1. Trong menu bên trái, click **"Clients"**
2. Tìm client có Client ID = **"test01"**
3. Click vào client **"test01"**

### 2.3 Cấu hình Settings
1. Đảm bảo đang ở tab **"Settings"**
2. Cuộn xuống phần **"Access settings"**

### 2.4 Cấu hình Valid redirect URIs
Trong trường **"Valid redirect URIs"**, đảm bảo có:
```
http://localhost:8082/auth/keycloak/callback
http://localhost:8082/*
```

### 2.5 Cấu hình Valid post logout redirect URIs (QUAN TRỌNG!)
Trong trường **"Valid post logout redirect URIs"**, thêm:
```
http://localhost:8082/auth/keycloak/logout-callback
http://localhost:8082/*
```

**Lưu ý:** Đây là trường riêng biệt, thường nằm dưới "Valid redirect URIs"

### 2.6 Cấu hình Web origins
Trong trường **"Web origins"**, đảm bảo có:
```
http://localhost:8082
```

### 2.7 Kiểm tra các settings khác
- **Access Type:** `public`
- **Standard Flow Enabled:** `ON`
- **Direct Access Grants Enabled:** `ON`

### 2.8 Lưu cấu hình
1. Cuộn xuống cuối trang
2. Click nút **"Save"**
3. Đợi thông báo "Success"

## ✅ Bước 3: Kiểm tra kết quả

### 3.1 Test logout URL lại
1. Quay lại: `http://localhost:8082/auth/keycloak/test-logout`
2. Click "Test This URL"
3. **Kết quả mong đợi:** Được redirect về `http://localhost:8082/auth/keycloak/logout-callback`

### 3.2 Test logout flow hoàn chỉnh
1. Đăng nhập vào ứng dụng
2. Click logout
3. **Kết quả mong đợi:** Được redirect về `/auth/login`

## 🚨 Nếu vẫn không hoạt động

### Kiểm tra lại cấu hình
1. Đảm bảo đã chọn đúng realm **"ttu"**
2. Đảm bảo đã chọn đúng client **"test01"**
3. Đảm bảo đã click **"Save"**

### Clear cache
1. Clear browser cache
2. Restart Keycloak server
3. Restart Next.js application

### Kiểm tra logs
1. Mở Browser DevTools → Console
2. Kiểm tra có error messages không
3. Mở Keycloak server logs

## 📸 Screenshots cần thiết

### Keycloak Client Settings
Cấu hình phải trông như thế này:

```
Valid redirect URIs:
http://localhost:8082/auth/keycloak/callback
http://localhost:8082/*

Valid post logout redirect URIs:
http://localhost:8082/auth/keycloak/logout-callback  
http://localhost:8082/*

Web origins:
http://localhost:8082

Access Type: public
Standard Flow Enabled: ON
```

## 🔍 Debug Tools

- **Test Logout:** `http://localhost:8082/auth/keycloak/test-logout`
- **Config Check:** `http://localhost:8082/auth/keycloak/config-check`
- **Debug Page:** `http://localhost:8082/auth/keycloak/debug`
- **Keycloak Admin:** `http://localhost:8080/admin`

## 📞 Hỗ trợ thêm

Nếu vẫn gặp vấn đề, hãy:
1. Chụp screenshot cấu hình Keycloak client
2. Copy/paste error messages từ browser console
3. Kiểm tra Keycloak server logs

## ✅ Checklist hoàn thành

- [ ] Đã truy cập Keycloak Admin Console
- [ ] Đã chọn realm "ttu"
- [ ] Đã tìm thấy client "test01"
- [ ] Đã thêm Valid redirect URIs
- [ ] Đã thêm Valid post logout redirect URIs
- [ ] Đã thêm Web origins
- [ ] Đã click Save
- [ ] Đã test logout URL thành công
- [ ] Đã test logout flow hoàn chỉnh thành công
