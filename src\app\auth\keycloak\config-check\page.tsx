'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, Typography, <PERSON>, Al<PERSON>, <PERSON><PERSON>, Di<PERSON><PERSON>, Chip } from '@mui/material';

import { CONFIG } from 'src/global-config';

// ----------------------------------------------------------------------

type ConfigCheck = {
  name: string;
  status: 'success' | 'error' | 'warning';
  message: string;
  value?: string;
};

export default function KeycloakConfigCheckPage() {
  const [checks, setChecks] = useState<ConfigCheck[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const performChecks = () => {
      const results: ConfigCheck[] = [];

      // Check environment variables
      results.push({
        name: 'KEYCLOAK_AUTHORITY',
        status: CONFIG.keycloak.authority ? 'success' : 'error',
        message: CONFIG.keycloak.authority ? 'Configured' : 'Missing environment variable',
        value: CONFIG.keycloak.authority,
      });

      results.push({
        name: 'KEYCLOAK_CLIENT_ID',
        status: CONFIG.keycloak.clientId ? 'success' : 'error',
        message: CONFIG.keycloak.clientId ? 'Configured' : 'Missing environment variable',
        value: CONFIG.keycloak.clientId,
      });

      results.push({
        name: 'KEYCLOAK_REDIRECT_URI',
        status: CONFIG.keycloak.redirectUri ? 'success' : 'error',
        message: CONFIG.keycloak.redirectUri ? 'Configured' : 'Missing environment variable',
        value: CONFIG.keycloak.redirectUri,
      });

      results.push({
        name: 'KEYCLOAK_POST_LOGOUT_REDIRECT_URI',
        status: CONFIG.keycloak.postLogoutRedirectUri ? 'success' : 'error',
        message: CONFIG.keycloak.postLogoutRedirectUri ? 'Configured' : 'Missing environment variable',
        value: CONFIG.keycloak.postLogoutRedirectUri,
      });

      // Check URL format
      if (CONFIG.keycloak.postLogoutRedirectUri) {
        const expectedUrl = `${window.location.origin}/auth/keycloak/logout-callback`;
        results.push({
          name: 'POST_LOGOUT_REDIRECT_URI_FORMAT',
          status: CONFIG.keycloak.postLogoutRedirectUri === expectedUrl ? 'success' : 'warning',
          message: CONFIG.keycloak.postLogoutRedirectUri === expectedUrl
            ? 'URL format is correct'
            : `Expected: ${expectedUrl}`,
          value: CONFIG.keycloak.postLogoutRedirectUri,
        });
      }

      setChecks(results);
      setLoading(false);
    };

    performChecks();
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return '✅';
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      default:
        return '❓';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'success';
      case 'error':
        return 'error';
      case 'warning':
        return 'warning';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography>Checking configuration...</Typography>
      </Box>
    );
  }

  const hasErrors = checks.some(check => check.status === 'error');
  const hasWarnings = checks.some(check => check.status === 'warning');

  return (
    <Box sx={{ p: 3, maxWidth: 1000, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        Keycloak Configuration Check
      </Typography>

      {/* Overall Status */}
      <Paper sx={{ p: 2, mb: 3 }}>
        {hasErrors ? (
          <Alert severity="error">
            Configuration has errors that need to be fixed
          </Alert>
        ) : hasWarnings ? (
          <Alert severity="warning">
            Configuration has warnings that should be reviewed
          </Alert>
        ) : (
          <Alert severity="success">
            Configuration looks good!
          </Alert>
        )}
      </Paper>

      {/* Configuration Checks */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Environment Variables
        </Typography>
        {checks.map((check, index) => (
          <Box key={index} sx={{ mb: 2, p: 2, border: '1px solid #e0e0e0', borderRadius: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="h6" sx={{ mr: 1 }}>
                {getStatusIcon(check.status)}
              </Typography>
              <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
                {check.name}
              </Typography>
              <Chip
                label={check.status.toUpperCase()}
                color={getStatusColor(check.status) as 'success' | 'error' | 'warning' | 'default'}
                size="small"
                sx={{ ml: 'auto' }}
              />
            </Box>
            <Typography variant="body2" color="text.secondary">
              {check.message}
            </Typography>
            {check.value && (
              <Typography variant="body2" sx={{ fontFamily: 'monospace', mt: 1, p: 1, bgcolor: '#f5f5f5', borderRadius: 1 }}>
                {check.value}
              </Typography>
            )}
          </Box>
        ))}
      </Paper>

      {/* Keycloak Client Configuration Guide */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Typography variant="h6" gutterBottom>
          Keycloak Client Configuration Required
        </Typography>
        <Typography variant="body2" paragraph>
          To fix the "Invalid redirect uri" error, configure these settings in Keycloak Admin Console:
        </Typography>

        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            1. Valid Redirect URIs:
          </Typography>
          <Box sx={{ fontFamily: 'monospace', fontSize: '0.875rem', bgcolor: '#f5f5f5', p: 1, borderRadius: 1 }}>
            {CONFIG.keycloak.redirectUri}<br />
            {window.location.origin}/*
          </Box>
        </Box>

        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            2. Valid Post Logout Redirect URIs: (QUAN TRỌNG!)
          </Typography>
          <Box sx={{ fontFamily: 'monospace', fontSize: '0.875rem', bgcolor: '#f5f5f5', p: 1, borderRadius: 1 }}>
            {CONFIG.keycloak.postLogoutRedirectUri}<br />
            {window.location.origin}/*
          </Box>
        </Box>

        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            3. Web Origins:
          </Typography>
          <Box sx={{ fontFamily: 'monospace', fontSize: '0.875rem', bgcolor: '#f5f5f5', p: 1, borderRadius: 1 }}>
            {window.location.origin}
          </Box>
        </Box>
      </Paper>

      {/* Quick Actions */}
      <Paper sx={{ p: 2 }}>
        <Typography variant="h6" gutterBottom>
          Quick Actions
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="contained"
            onClick={() => window.open('http://localhost:8080/admin', '_blank')}
          >
            Open Keycloak Admin
          </Button>
          <Button
            variant="outlined"
            onClick={() => window.location.reload()}
          >
            Recheck Configuration
          </Button>
        </Box>
      </Paper>
    </Box>
  );
}
